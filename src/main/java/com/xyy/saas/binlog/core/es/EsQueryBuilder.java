package com.xyy.saas.binlog.core.es;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.search.Highlight;
import co.elastic.clients.elasticsearch.core.search.HighlightField;
import co.elastic.clients.json.JsonData;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Elasticsearch查询构建器工具类
 * 提供fluent API构建各种ES查询
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class EsQueryBuilder {

    private EsQueryBuilder() {
        // 工具类不允许实例化
    }

    // ====== 基础查询 ======

    /**
     * 匹配所有文档
     */
    public static Query matchAllQuery() {
        return MatchAllQuery.of(m -> m)._toQuery();
    }

    /**
     * 精确匹配查询
     */
    public static Query termQuery(String field, String value) {
        return TermQuery.of(t -> t.field(field).value(FieldValue.of(value)))._toQuery();
    }

    /**
     * 精确匹配查询（数值）
     */
    public static Query termQuery(String field, Number value) {
        return TermQuery.of(t -> t.field(field).value(FieldValue.of(value.toString())))._toQuery();
    }

    /**
     * 精确匹配查询（布尔值）
     */
    public static Query termQuery(String field, Boolean value) {
        return TermQuery.of(t -> t.field(field).value(FieldValue.of(value)))._toQuery();
    }

    /**
     * 多值精确匹配
     */
    public static Query termsQuery(String field, List<String> values) {
        List<FieldValue> fieldValues = values.stream()
                .map(FieldValue::of)
                .toList();
        return TermsQuery.of(t -> t.field(field).terms(terms -> terms.value(fieldValues)))._toQuery();
    }

    /**
     * 模糊匹配查询
     */
    public static Query matchQuery(String field, String value) {
        return MatchQuery.of(m -> m.field(field).query(value))._toQuery();
    }

    /**
     * 短语匹配查询
     */
    public static Query matchPhraseQuery(String field, String phrase) {
        return MatchPhraseQuery.of(m -> m.field(field).query(phrase))._toQuery();
    }

    /**
     * 短语匹配查询
     */
    public static Query multiMatchPhraseQuery(List<String> fields, String phrase) {
        return MultiMatchQuery.of(m -> m.fields(fields).query(phrase).type(TextQueryType.Phrase))._toQuery();
    }

    /**
     * 通配符查询
     */
    public static Query wildcardQuery(String field, String pattern) {
        return WildcardQuery.of(w -> w.field(field).value(pattern))._toQuery();
    }

    /**
     * 前缀查询
     */
    public static Query prefixQuery(String field, String prefix) {
        return PrefixQuery.of(p -> p.field(field).value(prefix))._toQuery();
    }

    /**
     * 正则表达式查询
     */
    public static Query regexpQuery(String field, String regexp) {
        return RegexpQuery.of(r -> r.field(field).value(regexp))._toQuery();
    }

    /**
     * 存在性查询
     */
    public static Query existsQuery(String field) {
        return ExistsQuery.of(e -> e.field(field))._toQuery();
    }

    // ====== 范围查询 ======

    /**
     * 范围查询构建器
     */
    public static RangeQueryBuilder rangeQuery(String field) {
        return new RangeQueryBuilder(field);
    }

    public static class RangeQueryBuilder {
        private final String field;
        private Object gte;
        private Object gt;
        private Object lte;
        private Object lt;

        private RangeQueryBuilder(String field) {
            this.field = field;
        }

        public RangeQueryBuilder gte(Object value) {
            this.gte = value;
            return this;
        }

        public RangeQueryBuilder gt(Object value) {
            this.gt = value;
            return this;
        }

        public RangeQueryBuilder lte(Object value) {
            this.lte = value;
            return this;
        }

        public RangeQueryBuilder lt(Object value) {
            this.lt = value;
            return this;
        }

        public Query build() {
            return RangeQuery.of(r -> {
                r.field(field);
                if (gte != null) r.gte(JsonData.of(gte));
                if (gt != null) r.gt(JsonData.of(gt));
                if (lte != null) r.lte(JsonData.of(lte));
                if (lt != null) r.lt(JsonData.of(lt));
                return r;
            })._toQuery();
        }
    }

    // ====== 复合查询 ======

    /**
     * Bool查询构建器
     */
    public static BoolQueryBuilder boolQuery() {
        return new BoolQueryBuilder();
    }

    public static class BoolQueryBuilder {
        private final BoolQuery.Builder builder = new BoolQuery.Builder();

        public BoolQueryBuilder must(Query query) {
            builder.must(query);
            return this;
        }

        public BoolQueryBuilder mustNot(Query query) {
            builder.mustNot(query);
            return this;
        }

        public BoolQueryBuilder should(Query query) {
            builder.should(query);
            return this;
        }

        public BoolQueryBuilder filter(Query query) {
            builder.filter(query);
            return this;
        }

        public BoolQueryBuilder minimumShouldMatch(String minimumShouldMatch) {
            builder.minimumShouldMatch(minimumShouldMatch);
            return this;
        }

        public Query build() {
            return builder.build()._toQuery();
        }
    }

    // ====== 排序构建 ======

    /**
     * 字段排序（升序）
     */
    public static SortOptions sort(String field) {
        return sort(field, SortOrder.Asc);
    }

    /**
     * 字段排序
     */
    public static SortOptions sort(String field, SortOrder order) {
        return SortOptions.of(s -> s.field(f -> f.field(field).order(order)));
    }

    /**
     * 字段排序（字符串方向）
     */
    public static SortOptions sort(String field, String direction) {
        SortOrder order = "desc".equalsIgnoreCase(direction) ? SortOrder.Desc : SortOrder.Asc;
        return sort(field, order);
    }

    /**
     * 得分排序
     */
    public static SortOptions scoreSort() {
        return scoreSort(SortOrder.Desc);
    }

    /**
     * 得分排序
     */
    public static SortOptions scoreSort(SortOrder order) {
        return SortOptions.of(s -> s.score(sc -> sc.order(order)));
    }

    // ====== 高亮构建 ======

    /**
     * 创建高亮配置
     */
    public static Highlight highlight(String... fields) {
        return highlight(List.of(fields));
    }

    /**
     * 创建高亮配置
     */
    public static Highlight highlight(List<String> fields) {
        Highlight.Builder builder = new Highlight.Builder();

        Map<String, HighlightField> fieldsMap = fields.stream()
                .collect(java.util.stream.Collectors.toMap(
                        field -> field,
                        field -> HighlightField.of(h -> h)
                ));

        return builder.fields(fieldsMap).build();
    }

    /**
     * 创建高亮配置（带标签）
     */
    public static Highlight highlight(List<String> fields, String preTag, String postTag) {
        Highlight.Builder builder = new Highlight.Builder()
                .preTags(preTag)
                .postTags(postTag);

        Map<String, HighlightField> fieldsMap = fields.stream()
                .collect(java.util.stream.Collectors.toMap(
                        field -> field,
                        field -> HighlightField.of(h -> h)
                ));

        return builder.fields(fieldsMap).build();
    }

    // ====== 聚合构建 ======

    /**
     * 术语聚合
     */
    public static Aggregation termsAggregation(String field) {
        return Aggregation.of(a -> a.terms(t -> t.field(field)));
    }

    /**
     * 术语聚合（指定大小）
     */
    public static Aggregation termsAggregation(String field, int size) {
        return Aggregation.of(a -> a.terms(t -> t.field(field).size(size)));
    }

    /**
     * 日期直方图聚合
     */
    public static Aggregation dateHistogramAggregation(String field, String interval) {
        return Aggregation.of(a -> a.dateHistogram(dh -> dh
                .field(field)
                .fixedInterval(i -> i.time(interval))));
    }

    /**
     * 求和聚合
     */
    public static Aggregation sumAggregation(String field) {
        return Aggregation.of(a -> a.sum(s -> s.field(field)));
    }

    /**
     * 平均值聚合
     */
    public static Aggregation avgAggregation(String field) {
        return Aggregation.of(a -> a.avg(avg -> avg.field(field)));
    }

    /**
     * 最大值聚合
     */
    public static Aggregation maxAggregation(String field) {
        return Aggregation.of(a -> a.max(m -> m.field(field)));
    }

    /**
     * 最小值聚合
     */
    public static Aggregation minAggregation(String field) {
        return Aggregation.of(a -> a.min(m -> m.field(field)));
    }

    /**
     * 计数聚合
     */
    public static Aggregation countAggregation(String field) {
        return Aggregation.of(a -> a.valueCount(vc -> vc.field(field)));
    }

    // ====== 常用查询模板 ======

    /**
     * 时间范围查询
     */
    public static Query timeRangeQuery(String timeField, LocalDateTime start, LocalDateTime end) {
        return rangeQuery(timeField)
                .gte(start.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .lte(end.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .build();
    }

    /**
     * 多字段模糊搜索
     */
    public static Query multiMatchQuery(String query, String... fields) {
        return MultiMatchQuery.of(m -> m
                .query(query)
                .fields(List.of(fields)))._toQuery();
    }

    /**
     * ID列表查询
     */
    public static Query idsQuery(List<String> ids) {
        return IdsQuery.of(i -> i.values(ids))._toQuery();
    }

    /**
     * 组合条件查询（AND关系）
     */
    public static Query andQuery(Query... queries) {
        BoolQueryBuilder boolBuilder = boolQuery();
        for (Query query : queries) {
            boolBuilder.must(query);
        }
        return boolBuilder.build();
    }

    /**
     * 组合条件查询（OR关系）
     */
    public static Query orQuery(Query query, Query... queries) {
        BoolQueryBuilder boolBuilder = boolQuery().should(query);
        if (queries != null) {
            for (Query q : queries) {
                boolBuilder.should(q);
            }
        }
        return boolBuilder.minimumShouldMatch("1").build();
    }


    /**
     * 添加精确匹配查询
     *
     * @param field 字段名
     * @param value 查询值
     * @return Optional查询对象
     */
    public static Optional<Query> termQueryOpt(String field, Object value) {
        if (value == null || (value instanceof String && StringUtils.isBlank(value.toString()))) {
            return Optional.empty();
        }
        if (value instanceof Collection && ((Collection<?>) value).isEmpty()) {
            return Optional.empty();
        }

        Query query = switch (value) {
            case String string -> termQuery(field, string);
            case Boolean bool -> termQuery(field, bool);
            case Integer integer -> termQuery(field, integer);
            case Long longVal -> termQuery(field, longVal);
            case BigDecimal bigDecimal -> termQuery(field, bigDecimal.doubleValue());
            case Number number -> termQuery(field, number.doubleValue());
            case Collection<?> collection -> termsQuery(field, collection.stream().map(String::valueOf).toList());
            default -> null;
        };
        return Optional.ofNullable(query);
    }


    /**
     * 添加通配符查询
     *
     * @param field 字段名
     * @param value 查询值
     */
    public static Optional<Query> wildcardQueryOpt(String field, String value) {
        if (StringUtils.isBlank(value)) {
            return Optional.empty();
        }
        return Optional.of(WildcardQuery.of(w -> w.field(field).value("*" + value + "*"))._toQuery());
    }

    /**
     * 多字段匹配
     *
     * @param fields   字段数组
     * @param value    查询值
     * @param isPhrase 是否为短语匹配
     * @return 查询条件的Optional包装
     */
    public static Optional<Query> multiFieldMatchQueryOpt(String[] fields, String value, boolean isPhrase) {
        if (StringUtils.isBlank(value)) {
            return Optional.empty();
        }

        return Optional.of(MultiMatchQuery.of(m -> m.query(value).fields(List.of(fields)).type(isPhrase ? TextQueryType.Phrase : null))._toQuery());
    }
}