package com.xyy.saas.inquiry.hospital.api.hospital;

import com.xyy.saas.inquiry.pojo.hospital.InquiryHosDataConfigQueryParamDto;
import java.util.List;

/**
 * 医院数据配置服务api
 *
 * @Author:chen<PERSON><PERSON>i
 * @Date:2024/09/30 10:45
 */
public interface InquiryHospitalDataConfigApi {

    /**
     * 查询医院数据参数配置
     *
     * @param hospitalPref
     * @return
     */
    InquiryHosDataConfigQueryParamDto queryHosDataConfigParam(String hospitalPref);

    /**
     * 查询医院数据配置菜单id
     *
     * @param hospitalPrefs
     * @return
     */
    List<Long> queryHosDataConfigMenuIds();

}
