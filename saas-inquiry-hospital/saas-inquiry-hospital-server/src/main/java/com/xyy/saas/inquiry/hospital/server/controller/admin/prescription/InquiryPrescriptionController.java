package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static com.xyy.saas.inquiry.hospital.server.constant.PrescriptionConstant.PRESCRIPTION_PDF_EXPORT_MAX_SIZE;
import static com.xyy.saas.inquiry.hospital.server.constant.PrescriptionConstant.PRESCRIPTION_PDF_PRINT_MAX_SIZE;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.constant.ValidateGroup.Delete;
import com.xyy.saas.inquiry.constant.ValidateGroup.Update;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionAbandonVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionExcelRespAbandonVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionExcelRespTenantNameAbandonVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionExcelRespTenantNameVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionExcelRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionFlushReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPdfReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPricingVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionConvert;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionFlushService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionService;
import com.xyy.saas.inquiry.util.ThreadPoolManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 处方记录")
@RestController
@RequestMapping("/hospital/inquiry-prescription")
@Validated
public class InquiryPrescriptionController {

    @Resource
    private InquiryPrescriptionService inquiryPrescriptionService;

    @Resource
    private InquiryPrescriptionFlushService inquiryPrescriptionFlushService;

    @Autowired
    private TenantApi tenantApi;

    @PostMapping("/create")
    @Operation(summary = "创建处方记录")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:create')")
    public CommonResult<InquiryPrescriptionRespDTO> createInquiryPrescription(@Valid @RequestBody InquiryPrescriptionSaveReqVO createReqVO) {
        return success(inquiryPrescriptionService.createInquiryPrescription(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新处方记录")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:update')")
    public CommonResult<Boolean> updateInquiryPrescription(@Valid @RequestBody InquiryPrescriptionSaveReqVO updateReqVO) {
        inquiryPrescriptionService.updateInquiryPrescription(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除处方记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:delete')")
    public CommonResult<Boolean> deleteInquiryPrescription(@RequestParam("id") Long id) {
        inquiryPrescriptionService.deleteInquiryPrescription(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得处方记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    public CommonResult<InquiryPrescriptionRespVO> getInquiryPrescription(@RequestParam("id") Long id) {
        return success(inquiryPrescriptionService.getInquiryPrescription(id));
    }


    @PostMapping("/pricing")
    @Operation(summary = "处方划价")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:pricing')")
    public CommonResult<Long> pricingInquiryPrescription(@Valid @RequestBody InquiryPrescriptionPricingVO pricingVO) {
        return success(inquiryPrescriptionService.pricingInquiryPrescription(pricingVO));
    }


    @GetMapping("/page")
    @Operation(summary = "获得处方记录分页")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    public CommonResult<PageResult<InquiryPrescriptionRespVO>> getInquiryPrescriptionPage(@Valid InquiryPrescriptionPageReqVO pageReqVO) {
        pageReqVO.isWebQuery();
        return success(inquiryPrescriptionService.getInquiryPrescriptionPage(pageReqVO));
    }

    @PostMapping("/export-excel")
    @Operation(summary = "导出处方记录 Excel")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:export')")
    @ApiAccessLog(operateType = EXPORT)
    @Idempotent(timeout = 5) // 参数维度锁住5s
    public void exportInquiryPrescriptionExcel(@Valid @RequestBody InquiryPrescriptionPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(10000);
        pageReqVO.isWebQuery();
        List<InquiryPrescriptionExcelRespVO> list = inquiryPrescriptionService.getInquiryPrescriptionExcelList(pageReqVO);

        if (CommonStatusEnum.isDisable(pageReqVO.getEnable())) {
            // 废弃处方
            if (TenantConstant.isSystemTenant() || tenantApi.isHeadTenant()) {
                ExcelUtils.write(response, "处方记录.xls", "数据", InquiryPrescriptionExcelRespTenantNameAbandonVO.class,
                    BeanUtils.toBean(list, InquiryPrescriptionExcelRespTenantNameAbandonVO.class));
            } else {
                ExcelUtils.write(response, "处方记录.xls", "数据", InquiryPrescriptionExcelRespAbandonVO.class, BeanUtils.toBean(list, InquiryPrescriptionExcelRespAbandonVO.class));
            }
        } else {
            if (TenantConstant.isSystemTenant() || tenantApi.isHeadTenant()) {
                ExcelUtils.write(response, "处方记录.xls", "数据", InquiryPrescriptionExcelRespTenantNameVO.class,
                    InquiryPrescriptionConvert.INSTANCE.convertVO2ExcelVO1(list));
            } else {
                ExcelUtils.write(response, "处方记录.xls", "数据", InquiryPrescriptionExcelRespVO.class, list);
            }
        }
    }

    @PostMapping("/export-pdf")
    @Operation(summary = "导出处方Pdf")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:export')")
    @ApiAccessLog(operateType = EXPORT)
    @Idempotent(timeout = 3)
    public void exportInquiryPrescriptionPdf(@Valid @RequestBody InquiryPrescriptionPdfReqVO exportPdfVO, HttpServletResponse response) {
        exportPdfVO.setMaxSizeKey(PRESCRIPTION_PDF_EXPORT_MAX_SIZE);
        exportPdfVO.setOperate("导出");
        inquiryPrescriptionService.exportInquiryPrescriptionPdf(exportPdfVO, response);
    }


    @PostMapping("/batch-combiner-print")
    @Operation(summary = "批量拼接打印处方")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    @Idempotent(timeout = 3)
    public CommonResult<List<String>> batchCombinerPrintInquiryPrescription(@Valid @RequestBody InquiryPrescriptionPdfReqVO exportPdfVO) {
        exportPdfVO.setMaxSizeKey(PRESCRIPTION_PDF_PRINT_MAX_SIZE);
        exportPdfVO.setOperate("打印");
        return success(inquiryPrescriptionService.batchCombinerPrintInquiryPrescription(exportPdfVO));
    }


    @GetMapping("/print")
    @Operation(summary = "获得处方记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    public CommonResult<String> printInquiryPrescription(@RequestParam("id") Long id) {
        return success(inquiryPrescriptionService.printInquiryPrescription(id));
    }

    @PostMapping("/batch-print")
    @Operation(summary = "批量打印处方")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    public CommonResult<List<String>> batchPrintInquiryPrescription(@Valid @RequestBody InquiryPrescriptionPdfReqVO exportPdfVO) {
        exportPdfVO.setMaxSizeKey(PRESCRIPTION_PDF_PRINT_MAX_SIZE);
        exportPdfVO.setOperate("打印");
        return success(inquiryPrescriptionService.printInquiryPrescriptions(exportPdfVO));
    }

    @PutMapping("/abandon")
    @Operation(summary = "作废处方")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:abandon')")
    public CommonResult<Boolean> abandon(@Validated(Delete.class) @RequestBody InquiryPrescriptionAbandonVO abandonVO) {
        inquiryPrescriptionService.abandon(abandonVO);
        return success(true);
    }

    @PutMapping("/abandon-restore")
    @Operation(summary = "恢复作废处方")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:abandon')")
    public CommonResult<Boolean> abandonRestore(@Validated(Update.class) @RequestBody InquiryPrescriptionAbandonVO abandonVO) {
        inquiryPrescriptionService.abandonRestore(abandonVO);
        return success(true);
    }


    @GetMapping("/hos-patient-profile")
    @Operation(summary = "数据监管患者档案")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    public CommonResult<PageResult<InquiryPrescriptionRespVO>> hosPatientProfile(@Valid InquiryPrescriptionPageReqVO pageReqVO) {
        pageReqVO.isWebQuery();
        return success(inquiryPrescriptionService.hosPatientProfile(pageReqVO));
    }

    @PutMapping("/update-by-admin")
    @Operation(summary = "管理员更新处方记录")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:update')")
    public CommonResult<Boolean> updateInquiryPrescriptionByAdmin(@Valid @RequestBody InquiryPrescriptionSaveReqVO updateReqVO) {
        inquiryPrescriptionService.updateInquiryPrescriptionByAdmin(updateReqVO);
        return success(true);
    }


    @PostMapping("/flush")
    @Operation(summary = "后台刷处方", hidden = true)
    public CommonResult<?> flushInquiryPrescription(@Valid @RequestBody InquiryPrescriptionFlushReqVO flushReqVO) {
        flushReqVO.paramValid();
        ThreadPoolManager.execute(() -> inquiryPrescriptionFlushService.flushInquiryPrescription(flushReqVO));
        return success(true);
    }

    @GetMapping("/es-search-switch")
    @Operation(summary = "是否走es查询", hidden = true)
    public CommonResult<Boolean> esSearchSwitch() {
        return success(inquiryPrescriptionService.esSearchSwitch());
    }

}