package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.hospital.enums.HosDataConditionRuleType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Schema(description = "管理后台 - 医院数据参数配置分页 Request VO")
@Data
@Builder
public class InquiryHospitalDataConfigPageReqVO extends PageParam {

    @Schema(description = "互联网医院编号")
    @NotNull(message = "互联网医院编号不能为空")
    private String hospitalPref;

    private List<String> hospitalPrefs;
    /**
     * 选项类型 {@link HosDataConditionRuleType}
     */
    @Schema(description = "选项类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "选项类型不能为空")
    private String paramField;

    private List<String> paramFields;

    @Schema(description = "参数集合")
    private List<String> paramValues;


    @Schema(description = "名称")
    private String name;

}