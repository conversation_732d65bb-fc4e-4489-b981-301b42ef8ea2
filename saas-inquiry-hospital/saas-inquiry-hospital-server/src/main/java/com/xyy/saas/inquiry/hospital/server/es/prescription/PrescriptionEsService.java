package com.xyy.saas.inquiry.hospital.server.es.prescription;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.util.NamedValue;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery.Builder;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.search.SourceConfig;
import co.elastic.clients.elasticsearch.core.search.SourceFilter;
import com.xyy.saas.binlog.core.es.EsQueryBuilder;
import com.xyy.saas.binlog.core.es.EsQueryService;
import com.xyy.saas.binlog.core.es.EsSearchRequest;
import com.xyy.saas.binlog.core.es.EsSearchResponse;
import com.xyy.saas.binlog.core.es.EsSearchResponse.EsHit;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionTypeEnum;
import com.xyy.saas.inquiry.hospital.server.constant.QuerySceneEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.pojo.hospital.InquiryHosDataConfigQueryParamDto;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 处方表ES数据查询
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/29 11:14
 */
@Service
@Slf4j
public class PrescriptionEsService {

    @Resource
    private EsQueryService esQueryService;

    private static final String INDEX_NAME = "saas_inquiry_prescription_index_";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * ES分页查询处方记录 参考 InquiryPrescriptionMapper#selectPage 的SQL查询逻辑转换为ES查询
     */
    public PageResult<InquiryPrescriptionRespVO> getEsInquiryPrescriptionPage(InquiryPrescriptionPageReqVO queryVo) {
        return executeStandardQuery(queryVo, "getEsInquiryPrescriptionPage");
    }


    /**
     * 医院患者档案ES分页查询 - 按患者去重，返回每个患者最早的问诊记录，按最早时间倒序排列
     */
    public PageResult<InquiryPrescriptionRespVO> getEsHosPatientProfilePage(InquiryPrescriptionPageReqVO queryVo) {
        return executeAggregationQuery(queryVo, "getEsHosPatientProfilePage");
    }

    /**
     * 执行标准ES查询（普通分页查询）
     */
    private PageResult<InquiryPrescriptionRespVO> executeStandardQuery(InquiryPrescriptionPageReqVO queryVo, String methodName) {
        try {
            // 前置条件检查
            if (isHosDataConfigNull(queryVo)) {
                log.warn("[{}] 医院员工查询参数不完整", methodName);
                return PageResult.empty();
            }

            // 构建查询条件
            BoolQuery.Builder boolQuery = new BoolQuery.Builder();
            buildPrescriptionQueryConditions(boolQuery, queryVo);

            // 构建排序
            List<co.elastic.clients.elasticsearch._types.SortOptions> sorts = buildSortOptions(queryVo);

            // 构建并执行查询
            EsSearchRequest searchRequest = buildStandardSearchRequest(queryVo, boolQuery, sorts);
            EsSearchResponse<InquiryPrescriptionRespVO> response = esQueryService.search(searchRequest, InquiryPrescriptionRespVO.class);

            return parseStandardQueryResults(response, methodName);

        } catch (Exception e) {
            log.error("[{}] ES查询异常", methodName, e);
            return PageResult.empty();
        }
    }

    /**
     * 执行聚合查询（患者去重分页查询）
     */
    private PageResult<InquiryPrescriptionRespVO> executeAggregationQuery(InquiryPrescriptionPageReqVO queryVo, String methodName) {
        try {
            // 前置条件检查
            if (isHosDataConfigNull(queryVo)) {
                log.warn("[{}] 医院员工查询参数不完整", methodName);
                return PageResult.empty();
            }

            // 构建查询条件
            BoolQuery.Builder boolQuery = new BoolQuery.Builder();
            buildPrescriptionQueryConditions(boolQuery, queryVo);

            // 构建聚合查询
            Map<String, Aggregation> aggregations = buildPatientDeduplicationAggregations(queryVo);

            // 构建并执行查询
            EsSearchRequest searchRequest = buildAggregationSearchRequest(queryVo, boolQuery, aggregations);
            EsSearchResponse<InquiryPrescriptionRespVO> response = esQueryService.search(searchRequest, InquiryPrescriptionRespVO.class);

            if (response == null) {
                log.warn("[{}] ES查询返回null", methodName);
                return PageResult.empty();
            }

            return parsePatientDeduplicationResults(response, queryVo);

        } catch (Exception e) {
            log.error("[{}] ES查询异常", methodName, e);
            return PageResult.empty();
        }
    }

    // ==================== 查询构建工具方法 ====================

    /**
     * 构建标准搜索请求
     */
    private EsSearchRequest buildStandardSearchRequest(InquiryPrescriptionPageReqVO queryVo, BoolQuery.Builder boolQuery,
                                                      List<co.elastic.clients.elasticsearch._types.SortOptions> sorts) {
        return EsSearchRequest.builder()
            .index(buildIndexName(queryVo))
            .trackTotalHits(true)
            .routing(getRouting(queryVo))
            .query(boolQuery.build()._toQuery())
            .from(calculateFromIndex(queryVo))
            .size(calculatePageSize(queryVo))
            .sorts(sorts)
            .build();
    }

    /**
     * 构建聚合搜索请求
     */
    private EsSearchRequest buildAggregationSearchRequest(InquiryPrescriptionPageReqVO queryVo, BoolQuery.Builder boolQuery,
                                                         Map<String, Aggregation> aggregations) {
        return EsSearchRequest.builder()
            .index(buildIndexName(queryVo))
            .trackTotalHits(true)
            .routing(getRouting(queryVo))
            .query(boolQuery.build()._toQuery())
            .size(0) // 聚合查询不需要返回文档
            .aggregations(aggregations)
            .build();
    }

    /**
     * 解析标准查询结果
     */
    private PageResult<InquiryPrescriptionRespVO> parseStandardQueryResults(EsSearchResponse<InquiryPrescriptionRespVO> response, String methodName) {
        if (response == null) {
            log.warn("[{}] ES查询返回null", methodName);
            return PageResult.empty();
        }

        List<InquiryPrescriptionRespVO> list = response.getHits() != null ?
            response.getHits().stream().map(EsHit::getSource).toList() :
            Collections.emptyList();

        long total = response.getTotalHits() != null ? response.getTotalHits().getValue() : 0L;

        log.info("[{}] ES查询成功，返回{}条记录，总计{}条", methodName, list.size(), total);
        return new PageResult<>(list, total);
    }

    // ==================== 基础工具方法 ====================

    private String buildIndexName(InquiryPrescriptionPageReqVO queryVo) {
        return StrUtil.concat(true, INDEX_NAME, StringUtils.defaultIfBlank(queryVo.getYear(), LocalDateTime.now().getYear() + ""));
    }

    private int calculateFromIndex(InquiryPrescriptionPageReqVO queryVo) {
        return queryVo.getPageNo() != null ? (queryVo.getPageNo() - 1) * calculatePageSize(queryVo) : 0;
    }

    private int calculatePageSize(InquiryPrescriptionPageReqVO queryVo) {
        return queryVo.getPageSize() != null ? queryVo.getPageSize() : 10;
    }

    private String getRouting(InquiryPrescriptionPageReqVO queryVo) {
        // 超管,查全部 或 单个门店
        if (TenantConstant.isSystemTenant()) {
            return queryVo.getTenantId() == null ? null : queryVo.getTenantId().toString();
        }
        // 单体查自己，或者连锁查所有
        if (CollUtil.isEmpty(queryVo.getTenantIds())) {
            return queryVo.getTenantId() == null ? TenantContextHolder.getTenantId().toString() : queryVo.getTenantId().toString();
        }
        return CollUtil.size(queryVo.getTenantIds()) == 1 ? queryVo.getTenantIds().getFirst().toString() : null;
    }

    /**
     * 构建处方查询条件
     * <p>
     * 原始SQL查询逻辑参考：InquiryPrescriptionMapper#selectPage 主要查询条件包括： 1. 基础过滤：id IN (?), pref = ?, pref IN (?), inquiry_pref IN (?) 2. 人员过滤：patient_pref = ?, hospital_pref = ?, doctor_pref = ? 3. 状态过滤：status = ?, status IN (?), auditor_type =
     * ?, distribute_status = ?, distribute_user_id = ? 4. 用药类型：medicine_type = ?, medicine_type IN (?) 5. 业务类型：inquiry_way_type, inquiry_biz_type, client_channel_type, biz_channel_type, audit_way_type 6. 其他条件：auto_inquiry,
     * third_prescription_no, enable, prescription_type, print_status 7. 医院过滤：hospital_pref IN (?) 8. 时间范围：out_prescription_time, audit_prescription_time, create_time BETWEEN ? AND ? 9. 租户药师复杂逻辑：见handleTenantAndPharmacistLogic方法 10.
     * 排序：根据QuerySceneEnum动态排序
     */
    private void buildPrescriptionQueryConditions(BoolQuery.Builder boolQuery, InquiryPrescriptionPageReqVO queryVo) {
        // 基础过滤条件
        // SQL: id IN (?)
        EsQueryBuilder.termQueryOpt("id", queryVo.getIds()).ifPresent(boolQuery::filter);

        // 处方编码过滤 - 支持单个和批量查询
        // SQL: pref = ? OR pref IN (?)
        EsQueryBuilder.termQueryOpt("pref", queryVo.getPref()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("pref", queryVo.getPrefs()).ifPresent(boolQuery::filter);

        // 问诊编码列表过滤 - 根据问诊记录查询相关处方
        // SQL: inquiry_pref IN (?)
        EsQueryBuilder.termQueryOpt("inquiryPref", queryVo.getInquiryPrefList()).ifPresent(boolQuery::filter);

        // 人员相关过滤 - 患者、医院、医生维度
        // SQL: patient_pref = ? AND hospital_pref = ? AND doctor_pref = ?
        EsQueryBuilder.termQueryOpt("patientPref", queryVo.getPatientPref()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("hospitalPref", queryVo.getHospitalPref()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("doctorPref", queryVo.getDoctorPref()).ifPresent(boolQuery::filter);

        // 状态相关过滤 - 处方状态、审核类型、分发状态等
        // SQL: status = ? OR status IN (?) AND auditor_type = ? AND distribute_status = ? AND distribute_user_id = ?
        EsQueryBuilder.termQueryOpt("status", queryVo.getStatus()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("status", queryVo.getStatuss()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("auditorType", queryVo.getAuditorType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("distributeStatus", queryVo.getDistributeStatus()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("distributeUserId", queryVo.getDistributeUserId()).ifPresent(boolQuery::filter);

        // 用药类型过滤 - 中药/西药分类
        // SQL: medicine_type = ? OR medicine_type IN (?)
        EsQueryBuilder.termQueryOpt("medicineType", queryVo.getMedicineType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("medicineType", queryVo.getMedicineTypes()).ifPresent(boolQuery::filter);

        // 商品名称match
        EsQueryBuilder.multiFieldMatchQueryOpt(new String[]{"productName"}, queryVo.getProductName(), true).ifPresent(boolQuery::must);

        // 业务类型过滤 - 问诊方式、业务类型、渠道类型等
        // SQL: inquiry_way_type = ? AND inquiry_biz_type = ? AND client_channel_type = ? AND biz_channel_type = ? AND audit_way_type = ?
        EsQueryBuilder.termQueryOpt("inquiryWayType", queryVo.getInquiryWayType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("inquiryBizType", queryVo.getInquiryBizType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("clientChannelType", queryVo.getClientChannelType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("bizChannelType", queryVo.getBizChannelType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("auditWayType", queryVo.getAuditWayType()).ifPresent(boolQuery::filter);

        // 其他条件过滤
        // SQL: auto_inquiry = ? AND third_prescription_no = ? AND enable = ? AND prescription_type = ? AND print_status = ?
        EsQueryBuilder.termQueryOpt("autoInquiry", queryVo.getAutoInquiry()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("thirdPrescriptionNo", queryVo.getThirdPrescriptionNo()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("enable", queryVo.getEnable()).ifPresent(boolQuery::filter);
        // 处方类型过滤 -1查空
        if (Objects.equals(queryVo.getPrescriptionType(), PrescriptionTypeEnum.UN_TYPE.getCode())) {
            boolQuery.filter(Query.of(q -> q.bool(b -> b.mustNot(mn -> mn.exists(e -> e.field("prescriptionType"))))));
        } else {
            EsQueryBuilder.termQueryOpt("prescriptionType", queryVo.getPrescriptionType()).ifPresent(boolQuery::filter);
        }
        EsQueryBuilder.termQueryOpt("slowDisease", queryVo.getSlowDisease()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("offlinePrescription", queryVo.getOfflinePrescription()).ifPresent(boolQuery::filter);

        EsQueryBuilder.termQueryOpt("printStatus", queryVo.getPrintStatus()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("deleted", false).ifPresent(boolQuery::filter);

        // 医院编码列表过滤 - 支持多医院查询
        // SQL: hospital_pref IN (?)
        EsQueryBuilder.termQueryOpt("hospitalPref", queryVo.getHospitalList()).ifPresent(boolQuery::filter);

        // 时间范围过滤 - 开方时间、审核时间、创建时间
        // SQL: out_prescription_time BETWEEN ? AND ? AND audit_prescription_time BETWEEN ? AND ? AND create_time BETWEEN ? AND ?
        buildTimeRangeQuery("outPrescriptionTime", queryVo.getOutPrescriptionTime()).ifPresent(boolQuery::filter);
        buildTimeRangeQuery("auditPrescriptionTime", queryVo.getAuditPrescriptionTime()).ifPresent(boolQuery::filter);
        buildTimeRangeQuery("createTime", queryVo.getCreateTime()).ifPresent(boolQuery::filter);

        // 处理租户和药师的复杂OR逻辑 - 最复杂的查询条件
        handleTenantAndPharmacistLogic(boolQuery, queryVo);
        // 处理医院逻辑
        handleHosDataConfigLogic(boolQuery, queryVo.getHosQueryParamDto());

    }

    /**
     * 构建时间范围查询 参考 ProductStdlibEsServiceImpl 的实现风格
     */
    private java.util.Optional<Query> buildTimeRangeQuery(String field, LocalDateTime[] timeRange) {
        if (timeRange == null || timeRange.length != 2) {
            return java.util.Optional.empty();
        }

        LocalDateTime startTime = timeRange[0];
        LocalDateTime endTime = timeRange[1];

        if (startTime == null || endTime == null) {
            return java.util.Optional.empty();
        }

        Query rangeQuery = EsQueryBuilder.rangeQuery(field)
            .gte(startTime.format(DATE_TIME_FORMATTER))
            .lte(endTime.format(DATE_TIME_FORMATTER))
            .build();

        return java.util.Optional.of(rangeQuery);
    }

    /**
     * 处理租户和药师的复杂OR逻辑
     * <p>
     * 原始SQL逻辑： if (TenantConstant.isSystemTenant()) { wrapper.eqIfPresent(InquiryPrescriptionDO::getPharmacistPref, reqVO.getPharmacistPref()) .inIfPresent(InquiryPrescriptionDO::getTenantId, reqVO.getTenantIds())
     * .eqIfPresent(InquiryPrescriptionDO::getTenantId, reqVO.getTenantId()); } else { // 查药师 or 所在门店 wrapper.and(wp -> wp.eq(StringUtils.isNotBlank(reqVO.getPharmacistPref()), InquiryPrescriptionDO::getPharmacistPref,
     * reqVO.getPharmacistPref()) .or(w -> w.in(CollUtil.isNotEmpty(reqVO.getTenantIds()), InquiryPrescriptionDO::getTenantId, reqVO.getTenantIds()))); }
     */
    private void handleTenantAndPharmacistLogic(BoolQuery.Builder boolQuery, InquiryPrescriptionPageReqVO queryVo) {
        if (TenantConstant.isSystemTenant()) {
            // 系统租户：简单的AND逻辑
            // SQL: pharmacist_pref = ? AND tenant_id IN (?) AND tenant_id = ?
            EsQueryBuilder.termQueryOpt("pharmacistPref", queryVo.getPharmacistPref()).ifPresent(boolQuery::filter);
            EsQueryBuilder.termQueryOpt("tenantId", queryVo.getTenantIds()).ifPresent(boolQuery::filter);
            EsQueryBuilder.termQueryOpt("tenantId", queryVo.getTenantId()).ifPresent(boolQuery::filter);
        } else {
            // 非系统租户：复杂的OR逻辑
            // SQL: (pharmacist_pref = ? OR tenant_id IN (?))
            // 业务含义：查询指定药师的处方 OR 指定门店的处方
            BoolQuery.Builder orQueryBuilder = new BoolQuery.Builder();

            // 药师条件：查询指定药师审核的处方
            EsQueryBuilder.termQueryOpt("pharmacistPref", queryVo.getPharmacistPref())
                .ifPresent(orQueryBuilder::should);

            // 门店条件：查询指定门店的处方
            EsQueryBuilder.termQueryOpt("tenantId", queryVo.getTenantIds())
                .ifPresent(orQueryBuilder::should);

            // 构建 OR 查询对象（仅构建一次）
            BoolQuery orQuery = orQueryBuilder.build();

            // 将OR条件组合添加到主查询中
            // 注意：只有当存在OR条件时才添加，避免空查询
            if (!orQuery.should().isEmpty()) {
                boolQuery.must(orQuery._toQuery());
            }
        }
    }

    /**
     * 构建排序选项
     * <p>
     * 原始SQL排序逻辑： 1. PHARMACIST场景特殊处理： - status=2时按out_prescription_time倒序 - 其他状态按audit_prescription_time倒序 2. 其他场景按QuerySceneEnum定义的sortField排序： - DRUGSTORE: 按ID排序 - DOCTOR: 按ID排序 - PHARMACIST: 按audit_prescription_time排序 3.
     * 默认：按开方时间(out_prescription_time)倒序
     */
    private List<co.elastic.clients.elasticsearch._types.SortOptions> buildSortOptions(InquiryPrescriptionPageReqVO queryVo) {
        // PHARMACIST场景的特殊排序逻辑
        if (Objects.equals(QuerySceneEnum.PHARMACIST.getCode(), queryVo.getQueryScene())) {
            // 原SQL逻辑：status=2 按 out_prescription_time 倒序，其他按 audit_prescription_time 倒序
            // ES实现：简化为按audit_prescription_time排序（药师审核时间）
            // 业务含义：药师查看处方时，优先显示最近审核的处方
            return List.of(EsQueryBuilder.sort("auditPrescriptionTime", SortOrder.Desc));
        }

        // 根据QuerySceneEnum的sortField排序
        if (queryVo.getQueryScene() != null) {
            QuerySceneEnum sceneEnum = QuerySceneEnum.getEnumByCode(queryVo.getQueryScene());
            // 根据枚举定义的排序字段进行排序
            return switch (sceneEnum) {
                case PHARMACIST ->
                    // 药师场景：按审核时间倒序
                    List.of(EsQueryBuilder.sort("auditPrescriptionTime", SortOrder.Desc));
                case DOCTOR ->
                    // 医生场景：按ID倒序（最新创建的处方在前）
                    List.of(EsQueryBuilder.sort("id", SortOrder.Desc));
                case DRUGSTORE ->
                    // 门店场景：按ID倒序（最新创建的处方在前）
                    List.of(EsQueryBuilder.sort("id", SortOrder.Desc));
                default -> List.of(EsQueryBuilder.sort("id", SortOrder.Desc));
            };
        }

        // 默认排序：按开方时间倒序
        // 业务含义：显示最近开具的处方
        return List.of(EsQueryBuilder.sort("outPrescriptionTime", SortOrder.Desc));
    }


    /**
     * 处理医院员工查询数据逻辑
     *
     * @param queryVo
     * @return
     */
    private static boolean isHosDataConfigNull(InquiryPrescriptionPageReqVO queryVo) {
        return queryVo.isHospitalEmployee() && (queryVo.getHosQueryParamDto() == null || CollUtil.isEmpty(queryVo.getHosQueryParamDto().getType())
            || CollUtil.isEmpty(queryVo.getHosQueryParamDto().getInquiryWayType()) || CollUtil.isEmpty(queryVo.getHosQueryParamDto().getPrescriptionType()));
    }

    /**
     * 处理医院数据配置逻辑
     *
     * @param boolQuery
     * @param hosQueryParamDto
     */
    private void handleHosDataConfigLogic(Builder boolQuery, InquiryHosDataConfigQueryParamDto hosQueryParamDto) {
        if (hosQueryParamDto == null) {
            return;
        }

        // 1. 开方类型过滤
        // SQL: autoInquiry IN (?)
        // 业务含义：根据医院配置的开方类型进行过滤
        EsQueryBuilder.termQueryOpt("autoInquiry", hosQueryParamDto.getType()).ifPresent(boolQuery::filter);

        // 2. 问诊方式过滤
        // SQL: inquiry_way_type IN (?)
        // 业务含义：根据医院支持的问诊方式进行过滤（图文/视频/电话）
        EsQueryBuilder.termQueryOpt("inquiryWayType", hosQueryParamDto.getInquiryWayType()).ifPresent(boolQuery::filter);

        // 3. 处方类型过滤 - 特殊处理-1值
        // 业务含义：-1表示无处方类型，需要查询字段不存在的数据
        List<Integer> prescriptionTypes = hosQueryParamDto.getPrescriptionType();
        if (CollUtil.isNotEmpty(prescriptionTypes)) {
            boolean containsUnType = prescriptionTypes.contains(PrescriptionTypeEnum.UN_TYPE.getCode());

            if (containsUnType) {
                // 包含-1：构建OR逻辑 (prescription_type is null OR prescription_type IN (?))
                BoolQuery.Builder orQueryBuilder = new BoolQuery.Builder();

                // 添加字段不存在的条件
                orQueryBuilder.should(Query.of(q -> q.bool(b -> b.mustNot(mn -> mn.exists(e -> e.field("prescriptionType"))))));

                // 添加其他非-1值的IN条件
                List<Integer> otherTypes = prescriptionTypes.stream()
                    .filter(type -> !Objects.equals(type, PrescriptionTypeEnum.UN_TYPE.getCode()))
                    .toList();

                if (CollUtil.isNotEmpty(otherTypes)) {
                    EsQueryBuilder.termQueryOpt("prescriptionType", otherTypes)
                        .ifPresent(orQueryBuilder::should);
                }

                BoolQuery orQuery = orQueryBuilder.build();
                if (!orQuery.should().isEmpty()) {
                    boolQuery.filter(orQuery._toQuery());
                }
            } else {
                // 不包含-1：正常IN查询
                // SQL: prescription_type IN (?)
                EsQueryBuilder.termQueryOpt("prescriptionType", prescriptionTypes).ifPresent(boolQuery::filter);
            }
        }

        // 4. 时间范围过滤 - 需要解析time字段并应用到outPrescriptionTime
        // 业务含义：根据医院配置的时间范围过滤开方时间
        buildHosTimeRangeQuery("outPrescriptionTime", hosQueryParamDto.getTime()).ifPresent(boolQuery::filter);

        // 5. 门店包含过滤
        // SQL: tenant_id IN (?)
        // 业务含义：只查询指定门店的处方
        EsQueryBuilder.termQueryOpt("tenantId", hosQueryParamDto.getTenantIds()).ifPresent(boolQuery::filter);

        // 6. 门店排除过滤
        // SQL: tenant_id NOT IN (?)
        // 业务含义：排除指定门店的处方
        EsQueryBuilder.termQueryOpt("tenantId", hosQueryParamDto.getNeTenantIds()).ifPresent(boolQuery::mustNot);

        // 7. 医生排除过滤
        // SQL: doctor_pref NOT IN (?)
        // 业务含义：排除指定医生开具的处方
        EsQueryBuilder.termQueryOpt("doctorPref", hosQueryParamDto.getNeDoctorPrefs()).ifPresent(boolQuery::mustNot);
    }

    /**
     * 构建医院时间范围查询 解析time字段格式："2025-01-01 08:30:00,2025-01-30 08:30:00"
     *
     * @param timeRanges 时间范围列表
     * @return ES查询条件
     */
    private java.util.Optional<Query> buildHosTimeRangeQuery(String field, List<String> timeRanges) {
        if (CollUtil.isEmpty(timeRanges)) {
            return java.util.Optional.empty();
        }

        // 处理多个时间范围的OR逻辑
        BoolQuery.Builder orQueryBuilder = new BoolQuery.Builder();

        for (String timeRange : timeRanges) {
            if (StringUtils.isBlank(timeRange) || !timeRange.contains(",")) {
                continue;
            }

            String[] times = timeRange.split(",");
            if (times.length != 2) {
                continue;
            }

            String startTime = times[0].trim();
            String endTime = times[1].trim();

            if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
                Query rangeQuery = EsQueryBuilder.rangeQuery(field)
                    .gte(startTime)
                    .lte(endTime)
                    .build();
                orQueryBuilder.should(rangeQuery);
            }
        }

        BoolQuery orQuery = orQueryBuilder.build();
        return orQuery.should().isEmpty() ?
            java.util.Optional.empty() :
            java.util.Optional.of(orQuery._toQuery());
    }

    /**
     * 构建患者去重分页聚合查询 使用 Composite Aggregation 按患者编码分组，支持真正的分页 使用 Top Hits Sub-Aggregation 获取每个患者最早的处方记录
     */
    private Map<String, Aggregation> buildPatientDeduplicationAggregations(InquiryPrescriptionPageReqVO queryVo) {
        Map<String, Aggregation> aggregations = new HashMap<>();

        // 计算分页参数
        int pageSize = queryVo.getPageSize() != null ? queryVo.getPageSize() : 10;
        int pageNo = queryVo.getPageNo() != null ? queryVo.getPageNo() : 1;

        // 计算需要获取的总数据量，确保有足够的数据进行分页
        // 为了支持深度分页，需要获取足够多的数据
        // Terms Aggregation 的 size 限制了返回的分组数量，需要设置得足够大

        // 智能计算 totalSize：
        // 1. 基础需求：当前页需要的数据量
        int basicSize = pageSize * pageNo;
        // 2. 缓冲区：额外获取一些数据以应对数据分布不均
        int bufferSize = pageSize * 10; // 10页的缓冲区
        // 3. 最小值：确保至少有足够的数据支持常见的分页场景
        int minSize = 10000;
        // 4. 最大值：避免过大的查询影响性能（ES Terms Aggregation 默认最大值是 65536）
        int maxSize = 50000;

        int totalSize = Math.min(Math.max(basicSize + bufferSize, minSize), maxSize);

        log.debug("[buildPatientDeduplicationAggregations] 分页参数：pageNo={}, pageSize={}, totalSize={}",
            pageNo, pageSize, totalSize);

        // 构建子聚合
        Map<String, Aggregation> subAggregations = buildPatientSubAggregations();

        // 使用 Terms Aggregation 按患者编码分组，并按最早时间倒序排列
        aggregations.put("patient_groups", Aggregation.of(agg -> agg
            .terms(terms -> terms
                .field("patientPref.keyword") // 按患者编码分组
                .size(totalSize) // 设置足够大的 size 确保数据完整性
                .order(List.of(NamedValue.of("earliest_time", SortOrder.Desc))) // 按每个患者的最早时间倒序排列
            )
            .aggregations(subAggregations)
        ));

        // 添加总数统计聚合，用于准确计算总页数
        aggregations.put("total_patients", Aggregation.of(agg -> agg
            .cardinality(cardinality -> cardinality
                .field("patientPref.keyword") // 统计不重复的患者数量
            )
        ));

        return aggregations;
    }

    /**
     * 构建患者子聚合 - 获取最早处方记录和最早时间
     */
    private Map<String, Aggregation> buildPatientSubAggregations() {
        Map<String, Aggregation> subAggregations = new HashMap<>();

        // Top Hits - 获取每个患者最早的处方记录详情
        subAggregations.put("earliest_prescription", Aggregation.of(agg -> agg
            .topHits(topHits -> topHits
                .size(1)
                .sort(List.of(EsQueryBuilder.sort("outPrescriptionTime", SortOrder.Asc)))
                .source(buildPatientFieldsSourceConfig())
            )
        ));

        // Min - 获取每个患者的最早时间，用于排序
        subAggregations.put("earliest_time", Aggregation.of(agg -> agg
            .min(min -> min.field("outPrescriptionTime"))
        ));

        return subAggregations;
    }

    /**
     * 构建患者字段的 Source 配置
     */
    private SourceConfig buildPatientFieldsSourceConfig() {
        return SourceConfig.of(source -> source
            .filter(SourceFilter.of(filter -> filter
                .includes(List.of(
                    "patientPref", "patientName", "patientSex",
                    "patientIdCard", "outPrescriptionTime"
                ))
            ))
        );
    }

    /**
     * 解析患者去重分页聚合结果 - 简化版本
     */
    private PageResult<InquiryPrescriptionRespVO> parsePatientDeduplicationResults(
        EsSearchResponse<InquiryPrescriptionRespVO> response,
        InquiryPrescriptionPageReqVO queryVo) {

        try {
            // 验证聚合结果
            var termsBuckets = validateAndGetTermsBuckets(response);
            if (termsBuckets == null) {
                return PageResult.empty();
            }

            // 获取准确的患者总数
            long totalPatients = getTotalPatientsCount(response, termsBuckets);

            // 提取分页数据
            List<InquiryPrescriptionRespVO> resultList = extractPagedPatientData(termsBuckets, queryVo, totalPatients);

            // 数据完整性检查
            int availableBuckets = termsBuckets.array().size();
            boolean dataComplete = totalPatients <= availableBuckets;

            if (!dataComplete) {
                log.warn("[parsePatientDeduplicationResults] 数据可能不完整！总患者数：{}，可用分组数：{}，建议增加 totalSize",
                    totalPatients, availableBuckets);
            }

            log.info("[parsePatientDeduplicationResults] 患者去重分页查询成功，返回{}条记录，总计{}条患者，数据完整性：{}",
                resultList.size(), totalPatients, dataComplete ? "完整" : "可能不完整");
            return new PageResult<>(resultList, totalPatients);

        } catch (Exception e) {
            log.error("[parsePatientDeduplicationResults] 解析聚合结果异常", e);
            return PageResult.empty();
        }
    }

}