package com.xyy.saas.inquiry.hospital.server.service.hospital;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pojo.hospital.InquiryHosDataConfigQueryParamDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHosDataConfigConditionVo;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDataConfigPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDataConfigRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDataConfigSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDataConfigTableSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDataConfigDO;
import java.util.List;

/**
 * 医院数据参数配置 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryHospitalDataConfigService {

    /**
     * 创建医院数据参数配置 saveInquiryHospitalDataConfig
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer saveInquiryHospitalDataConfig(InquiryHospitalDataConfigSaveReqVO createReqVO);

    /**
     * 批量保存医生区域门店数据
     *
     * @param tableSaveReqVO
     * @return
     */
    Integer saveDoctorAreaStore(InquiryHospitalDataConfigTableSaveReqVO tableSaveReqVO);

    /**
     * 批量删除医院数据参数配置
     *
     * @param ids 编号
     */
    void deleteInquiryHospitalDataConfigListByIds(List<Long> ids);

    /**
     * 获得医院数据参数配置
     *
     * @param id 编号
     * @return 医院数据参数配置
     */
    InquiryHospitalDataConfigDO getInquiryHospitalDataConfig(Long id);

    /**
     * 获得医院数据参数配置分页
     *
     * @param pageReqVO 分页查询
     * @return 医院数据参数配置分页
     */
    PageResult<InquiryHospitalDataConfigRespVO> getInquiryHospitalDataConfigPage(InquiryHospitalDataConfigPageReqVO pageReqVO);

    /**
     * 获取医院数据参数配置条件
     *
     * @param hospitalPref
     * @return
     */
    InquiryHosDataConfigConditionVo getInquiryHospitalDataConfigCondition(String hospitalPref);

    /**
     * 查询医院数据参数配置
     *
     * @param hospitalPref
     * @return
     */
    InquiryHosDataConfigQueryParamDto queryHosDataConfigParam(String hospitalPref);

    /**
     * 获取医院数据参数配置菜单ID
     *
     * @param hospitalPrefs
     * @return
     */
    List<Long> queryHosDataConfigMenuIds(List<String> hospitalPrefs);
}