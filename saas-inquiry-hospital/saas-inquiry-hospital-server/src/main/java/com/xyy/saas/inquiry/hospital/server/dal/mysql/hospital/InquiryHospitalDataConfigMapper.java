package com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDataConfigPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDataConfigDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 医院数据参数配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryHospitalDataConfigMapper extends BaseMapperX<InquiryHospitalDataConfigDO> {

    default PageResult<InquiryHospitalDataConfigDO> selectPage(InquiryHospitalDataConfigPageReqVO reqVO) {
        return selectPage(reqVO, getInquiryHospitalDataConfigDOLambdaQueryWrapperX(reqVO));
    }

    default List<InquiryHospitalDataConfigDO> selectList(InquiryHospitalDataConfigPageReqVO reqVO) {
        return selectList(getInquiryHospitalDataConfigDOLambdaQueryWrapperX(reqVO));
    }

    private static LambdaQueryWrapperX<InquiryHospitalDataConfigDO> getInquiryHospitalDataConfigDOLambdaQueryWrapperX(InquiryHospitalDataConfigPageReqVO reqVO) {
        return new LambdaQueryWrapperX<InquiryHospitalDataConfigDO>()
            .eqIfPresent(InquiryHospitalDataConfigDO::getHospitalPref, reqVO.getHospitalPref())
            .inIfPresent(InquiryHospitalDataConfigDO::getHospitalPref, reqVO.getHospitalPrefs())
            .eqIfPresent(InquiryHospitalDataConfigDO::getParamField, reqVO.getParamField())
            .inIfPresent(InquiryHospitalDataConfigDO::getParamField, reqVO.getParamFields())
            .inIfPresent(InquiryHospitalDataConfigDO::getParamValue, reqVO.getParamValues())
            .orderByDesc(InquiryHospitalDataConfigDO::getId);
    }

    void deleteByFields(String hospitalPref, List<String> paramFields);

    int insertOrUpdateBatch(@Param("list") List<InquiryHospitalDataConfigDO> list);

}