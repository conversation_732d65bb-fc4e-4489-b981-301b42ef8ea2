package com.xyy.saas.inquiry.hospital.server.api.hospital;

import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalDataConfigApi;
import com.xyy.saas.inquiry.pojo.hospital.InquiryHosDataConfigQueryParamDto;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDataConfigService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import java.util.List;

/**
 * 医院数据配置服务api
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/30 10:45
 */
@DubboService
public class InquiryHospitalDataConfigApiImpl implements InquiryHospitalDataConfigApi {

    @Resource
    private InquiryHospitalDataConfigService inquiryHospitalDataConfigService;

    @Override
    public InquiryHosDataConfigQueryParamDto queryHosDataConfigParam(String hospitalPref) {
        return inquiryHospitalDataConfigService.queryHosDataConfigParam(hospitalPref);
    }

    @Override
    public List<Long> queryHosDataConfigMenuIds() {
        return inquiryHospitalDataConfigService.queryHosDataConfigMenuIds(null);
    }
}
