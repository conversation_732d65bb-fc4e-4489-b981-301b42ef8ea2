package com.xyy.saas.inquiry.hospital.server.service.hospital;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSONArray;
import com.xyy.saas.inquiry.drugstore.api.user.UserApi;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHosDataConfigDto;
import com.xyy.saas.inquiry.pojo.hospital.InquiryHosDataConfigQueryParamDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHosDataConfigRespDto;
import com.xyy.saas.inquiry.hospital.enums.HosDataConditionRuleType;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHosDataConfigConditionVo;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDataConfigPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDataConfigRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDataConfigSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDataConfigTableSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.hospital.HospitalDataConfigConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDataConfigDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalDataConfigMapper;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.condition.ConditionRuleOp;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 医院数据参数配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryHospitalDataConfigServiceImpl implements InquiryHospitalDataConfigService {

    @Resource
    private InquiryHospitalDataConfigMapper inquiryHospitalDataConfigMapper;

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private HospitalEmployeeService hospitalEmployeeService;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private UserApi userApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveInquiryHospitalDataConfig(InquiryHospitalDataConfigSaveReqVO createReqVO) {
        InquiryHosDataConfigDto inquiryHosDataConfigDto = HospitalDataConfigConvert.INSTANCE.convertDto(createReqVO);
        // 转换存储
        List<InquiryHosDataConfigRespDto> inquiryHosDataConfigRespDtos = HosDataConditionRuleType.convertSaveDo(inquiryHosDataConfigDto);
        // 先删
        inquiryHospitalDataConfigMapper.deleteByFields(createReqVO.getHospitalPref(), HosDataConditionRuleType.normalField());
        // 再增
        if (CollUtil.isNotEmpty(inquiryHosDataConfigRespDtos)) {
            inquiryHospitalDataConfigMapper.insert(HospitalDataConfigConvert.INSTANCE.convertDo(inquiryHosDataConfigRespDtos));
        }
        return inquiryHosDataConfigRespDtos.size();
    }

    @Override
    public Integer saveDoctorAreaStore(InquiryHospitalDataConfigTableSaveReqVO tableSaveReqVO) {
        List<InquiryHosDataConfigRespDto> list = new ArrayList<>();

        if (CollUtil.isNotEmpty(tableSaveReqVO.getAreas())) {
            List<InquiryHosDataConfigRespDto> respDtos = HospitalDataConfigConvert.INSTANCE.convertInquiryHosDataConfigRespDtos(tableSaveReqVO.getAreas().stream().map(String::valueOf).toList(), tableSaveReqVO);
            list.addAll(respDtos);
        }
        if (CollUtil.isNotEmpty(tableSaveReqVO.getParamValues())) {
            List<InquiryHosDataConfigRespDto> respDtos = HospitalDataConfigConvert.INSTANCE.convertInquiryHosDataConfigRespDtos(tableSaveReqVO.getParamValues(), tableSaveReqVO);
            list.addAll(respDtos);
        }

        List<InquiryHospitalDataConfigDO> dataConfigDOS = HospitalDataConfigConvert.INSTANCE.convertDo(list);
        if (CollUtil.isNotEmpty(dataConfigDOS)) {
            inquiryHospitalDataConfigMapper.insertOrUpdateBatch(dataConfigDOS);
        }
        return dataConfigDOS.size();
    }


    @Override
    public void deleteInquiryHospitalDataConfigListByIds(List<Long> ids) {
        // 删除
        inquiryHospitalDataConfigMapper.deleteByIds(ids);
    }


    @Override
    public InquiryHospitalDataConfigDO getInquiryHospitalDataConfig(Long id) {
        return inquiryHospitalDataConfigMapper.selectById(id);
    }

    @Override
    public PageResult<InquiryHospitalDataConfigRespVO> getInquiryHospitalDataConfigPage(InquiryHospitalDataConfigPageReqVO pageReqVO) {

        switch (HosDataConditionRuleType.fromField(pageReqVO.getParamField())) {
            case HosDataConditionRuleType.DOCTOR -> {
                if (StringUtils.isNotBlank(pageReqVO.getName())) {
                    List<InquiryDoctorDO> doctorList = inquiryDoctorService.getInquiryDoctorList(InquiryDoctorPageReqVO.builder().name(pageReqVO.getName()).build());
                    if (CollUtil.isEmpty(doctorList)) {
                        return PageResult.empty();
                    }
                    pageReqVO.setParamValues(doctorList.stream().map(InquiryDoctorDO::getPref).toList());
                }
            }
            case HosDataConditionRuleType.STORE -> {
                if (StringUtils.isNotBlank(pageReqVO.getName())) {
                    final List<TenantDto> tenantList = tenantApi.getTenantList(pageReqVO.getName());
                    if (CollUtil.isEmpty(tenantList)) {
                        return PageResult.empty();
                    }
                    pageReqVO.setParamValues(tenantList.stream().map(TenantDto::getId).map(Object::toString).toList());
                }
            }
        }

        PageResult<InquiryHospitalDataConfigDO> pageResult = inquiryHospitalDataConfigMapper.selectPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }

        List<InquiryHospitalDataConfigRespVO> respVOS = HospitalDataConfigConvert.INSTANCE.convertVos(pageResult.getList());

        // 组装参数
        switch (HosDataConditionRuleType.fromField(pageReqVO.getParamField())) {
            case HosDataConditionRuleType.DOCTOR -> {
                List<String> doctorPrefs = respVOS.stream().map(InquiryHospitalDataConfigRespVO::getParamValue).distinct().toList();
                Map<String, String> doctorMap = inquiryDoctorService.getInquiryDoctorList(InquiryDoctorPageReqVO.builder().doctorPrefs(doctorPrefs).build()).stream()
                    .collect(Collectors.toMap(InquiryDoctorDO::getPref, InquiryDoctorDO::getName, (a, b) -> b));
                respVOS.forEach(item -> {
                    item.setParamValue(doctorMap.getOrDefault(item.getParamValue(), "").concat("(").concat(item.getParamValue()).concat(")"));
                });
            }
            case HosDataConditionRuleType.STORE -> {
                List<Long> tenantIds = respVOS.stream().map(InquiryHospitalDataConfigRespVO::getParamValue).map(Long::valueOf).distinct().toList();
                Map<Long, TenantDto> tenantDtoMap = tenantApi.getTenantListMap(tenantIds);
                respVOS.forEach(item -> {
                    item.setParamValue(tenantDtoMap.getOrDefault(Long.valueOf(item.getParamValue()), new TenantDto()).getName());
                });
            }
            case HosDataConditionRuleType.AREA -> respVOS.forEach(item -> {
                Optional.ofNullable(AreaUtils.getArea(Integer.valueOf(item.getParamValue()))).ifPresent(area ->
                    item.setParamValue(area.getParent().getParent().getName().concat("/").concat(area.getParent().getName()).concat("/").concat(area.getName())));
            });
        }
        return new PageResult<>(respVOS, pageResult.getTotal());
    }


    @Override
    public InquiryHosDataConfigConditionVo getInquiryHospitalDataConfigCondition(String hospitalPref) {

        // 获取用户信息
        AdminUserRespDTO userInfo = userApi.getUser();
        if (!RoleCodeEnum.isHospitalEmployee(userInfo.getRoleCodes())) {
            return new InquiryHosDataConfigConditionVo();
        }
        List<HospitalBindRespVO> hospitalBindRespVOS = hospitalEmployeeService.getBindHospitalsByUserId(userInfo.getId());
        List<String> hospitalPrefs = hospitalBindRespVOS.stream().map(HospitalBindRespVO::getHospitalPref).toList();
        if (CollUtil.size(hospitalPrefs) > 1 && StringUtils.isBlank(hospitalPref)) {
            return new InquiryHosDataConfigConditionVo();
        }
        List<InquiryHospitalDataConfigDO> dataConfigDOS = inquiryHospitalDataConfigMapper.selectList(
            InquiryHospitalDataConfigPageReqVO.builder().hospitalPref(StringUtils.isBlank(hospitalPref) ? hospitalPrefs.getFirst() : hospitalPref)
                .paramFields(HosDataConditionRuleType.queryField()).build());

        InquiryHosDataConfigQueryParamDto queryParamDto = new InquiryHosDataConfigQueryParamDto();

        dataConfigDOS.forEach(d -> {

            switch (HosDataConditionRuleType.fromField(d.getParamField())) {
                case HosDataConditionRuleType.TYPE -> queryParamDto.addType(JSONArray.parseArray(d.getParamValue(), Integer.class));
                case HosDataConditionRuleType.INQUIRY_TYPE -> queryParamDto.addInquiryWayType(JSONArray.parseArray(d.getParamValue(), Integer.class));
                case HosDataConditionRuleType.PRESCRIPTION_TYPE -> queryParamDto.addPrescriptionType(JSONArray.parseArray(d.getParamValue(), Integer.class));
            }
        });

        return HospitalDataConfigConvert.INSTANCE.convertVo(queryParamDto);
    }

    @Override
    public InquiryHosDataConfigQueryParamDto queryHosDataConfigParam(String hospitalPref) {
        InquiryHosDataConfigQueryParamDto queryParamDto = new InquiryHosDataConfigQueryParamDto();
        // 查询医院所有配置参数
        List<InquiryHospitalDataConfigDO> dataConfigDOS = inquiryHospitalDataConfigMapper.selectList(InquiryHospitalDataConfigDO::getHospitalPref, hospitalPref);

        // 处理区域门店
        List<Long> tenantIds = new ArrayList<>();
        List<String> areaCode = dataConfigDOS.stream()
            .filter(d -> StringUtils.equals(d.getParamField(), HosDataConditionRuleType.AREA.getField()))
            .map(InquiryHospitalDataConfigDO::getParamValue).filter(Objects::nonNull).toList();
        if (CollUtil.isNotEmpty(areaCode)) {
            List<TenantDto> tenantList = tenantApi.getTenantListByAreaCodes(areaCode);
            tenantIds.addAll(tenantList.stream().map(TenantDto::getId).toList());
        }
        // 处理存在门店
        List<Long> tids = dataConfigDOS.stream()
            .filter(d -> StringUtils.equals(d.getParamField(), HosDataConditionRuleType.STORE.getField()) && StringUtils.equals(d.getRuleOp(), ConditionRuleOp.EQ.getType()))
            .map(d -> NumberUtil.parseLong(d.getParamValue(), null)).filter(Objects::nonNull).toList();
        tenantIds.addAll(tids);
        // 处理排他门店
        List<Long> neTids = dataConfigDOS.stream()
            .filter(d -> StringUtils.equals(d.getParamField(), HosDataConditionRuleType.STORE.getField()) && StringUtils.equals(d.getRuleOp(), ConditionRuleOp.NE.getType()))
            .map(d -> NumberUtil.parseLong(d.getParamValue(), null)).filter(Objects::nonNull).toList();

        dataConfigDOS.forEach(d -> {

            switch (HosDataConditionRuleType.fromField(d.getParamField())) {
                case HosDataConditionRuleType.TYPE -> queryParamDto.addType(JSONArray.parseArray(d.getParamValue(), Integer.class));
                case HosDataConditionRuleType.INQUIRY_TYPE -> queryParamDto.addInquiryWayType(JSONArray.parseArray(d.getParamValue(), Integer.class));
                case HosDataConditionRuleType.PRESCRIPTION_TYPE -> queryParamDto.addPrescriptionType(JSONArray.parseArray(d.getParamValue(), Integer.class));
                case HosDataConditionRuleType.TIME -> queryParamDto.addTime(JSONArray.parseArray(d.getParamValue(), String.class));
                case HosDataConditionRuleType.DOCTOR -> queryParamDto.addNeDoctorPrefs(JSONArray.parseArray(d.getParamValue(), String.class));
                case HosDataConditionRuleType.STORE -> {
                    queryParamDto.addTenantIds(tenantIds);
                    queryParamDto.addNeTenantIds(neTids);
                }
            }
        });

        return queryParamDto;
    }

    @Override
    public List<Long> queryHosDataConfigMenuIds(List<String> hospitalPrefs) {

        // 获取用户信息
        AdminUserRespDTO userInfo = userApi.getUser();
        if (CollUtil.isEmpty(hospitalPrefs) && !RoleCodeEnum.isHospitalEmployee(userInfo.getRoleCodes())) {
            return List.of();
        }
        List<HospitalBindRespVO> hospitalBindRespVOS = hospitalEmployeeService.getBindHospitalsByUserId(userInfo.getId());
        List<String> prefs = hospitalBindRespVOS.stream().map(HospitalBindRespVO::getHospitalPref).toList();
        if (CollUtil.isEmpty(prefs) && CollUtil.isEmpty(hospitalPrefs)) {
            return List.of();
        }
        List<InquiryHospitalDataConfigDO> dataConfigDOS = inquiryHospitalDataConfigMapper.selectList(
            InquiryHospitalDataConfigPageReqVO.builder().hospitalPrefs(CollUtil.isEmpty(prefs) ? hospitalPrefs : prefs)
                .paramField(HosDataConditionRuleType.MENU.getField()).build());

        return dataConfigDOS.stream().map(i -> NumberUtil.parseLong(i.getParamValue(), null))
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }
}