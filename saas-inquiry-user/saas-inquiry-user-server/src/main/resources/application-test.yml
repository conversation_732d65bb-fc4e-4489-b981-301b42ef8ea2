spring:
  cloud:
    nacos:
      server-addr: 172.20.7.26:8848
      discovery:
        namespace: ${spring.profiles.active}
        group: http
        register-enabled: false
      config:
        namespace: ${spring.profiles.active}
        group: DEFAULT_GROUP
        server-addr: 172.20.7.26:8848
        prefix: ${spring.application.name}
        file-extension: yaml
  config:
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.yaml
      - optional:nacos:inquiry-global-${spring.profiles.active}.yaml
  datasource:
    dynamic:
      datasource:
        master:
          url: ************************************************************************************************************************************************************************************* # MODE 使用 MySQL 模式；DATABASE_TO_UPPER 配置表和字段使用小写
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe
        slave: # 模拟从库，可根据自己需要修改 # 模拟从库，可根据自己需要修改
          lazy: true # 开启懒加载，保证启动速度
          url: ************************************************************************************************************************************************************************************* # MODE 使用 MySQL 模式；DATABASE_TO_UPPER 配置表和字段使用小写
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe
  data:
    redis:
      #      host: ************** # 地址
      host: db01-medicare-test.redis.ybm100.top # 地址
      port: 30002 # 端口
      password: JEf8CVrnY0G3RPEZ # 密码，建议生产环境开启
      database: 15
  #定时任务 quartz 配置 ---------------------
  quartz:
    auto-startup: true # 本地开发环境，尽量不要开启 Job
    scheduler-name: schedulerName # Scheduler 名字。默认为 schedulerName
    job-store-type: jdbc # Job 存储器类型。默认为 memory 表示内存，可选 jdbc 使用数据库。
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    properties: # 添加 Quartz Scheduler 附加属性，更多可以看 http://www.quartz-scheduler.org/documentation/2.4.0-SNAPSHOT/configuration.html 文档
      org:
        quartz:
          # Scheduler 相关配置
          scheduler:
            instanceName: schedulerName
            instanceId: AUTO # 自动生成 instance ID
          # JobStore 相关配置
          jobStore:
            # JobStore 实现类。可见博客：https://blog.csdn.net/weixin_42458219/article/details/122247162
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            isClustered: false # 是集群模式
            clusterCheckinInterval: 15000 # 集群检查频率，单位：毫秒。默认为 15000，即 15 秒
            misfireThreshold: 60000 # misfire 阀值，单位：毫秒。
          # 线程池相关配置
          threadPool:
            threadCount: 25 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级
            class: org.quartz.simpl.SimpleThreadPool # 线程池类型
    jdbc: # 使用 JDBC 的 JobStore 的时候，JDBC 的配置
      initialize-schema: NEVER # 是否自动使用 SQL 初始化 Quartz 表结构。这里设置成 never ，我们手动创建表结构。

  # Elasticsearch配置
  elasticsearch:
    uris: ${ES_HOSTS:es001-inquiry-test.elasticsearch.ybm100.top:19200}
    connection-timeout: 5s
    socket-timeout: 30s



rocketmq:
  name-server: **********:9876
  #  topic:
  #    inquiry-topic: xyy_saas_inquiry_topic_test
  producer:
    group: saas_inquiry_system

external:
  rocketmq:
    middle:
      nameServer: mq1-me-test.rocketmq.ybm100.top:9876;mq2-me-test.rocketmq.ybm100.top:9876;mq3-me-test.rocketmq.ybm100.top:9876;mq4-me-test.rocketmq.ybm100.top:9876
    saas:
      nameServer: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876

logging:
  level:
    com.baomidou.mybatisplus: info

#event:
#  bus:
#    append-profile: true

yudao:
  swagger:
    http-domain: https://inquiry.test.ybm100.com

# 百度api-sdk
#baidu:
#  api:
#    face: # 人脸识别
#      groupId: inquiry_test
#      appId: 11
#      appKey: 22
#      secretKey: 33
# 腾讯api-sdk
tencent:
  api:
    face: # 人脸识别
      group-id: inquiry_test
      secret-id: AKIDGOK3Zsx40ldWIEIE5uqcTDpY6RaWFm6D
      secret-key: wfBTYQ4VnuqZ4MSPBwXw7A1f6LOFsXHs
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl