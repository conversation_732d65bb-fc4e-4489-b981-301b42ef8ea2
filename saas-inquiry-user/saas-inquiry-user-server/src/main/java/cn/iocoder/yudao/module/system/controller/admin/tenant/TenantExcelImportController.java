package cn.iocoder.yudao.module.system.controller.admin.tenant;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.framework.config.ImportLockFailureStrategy;
import cn.iocoder.yudao.module.system.service.tenant.TenantExcelImportService;
import com.baomidou.lock.annotation.Lock4j;
import com.xyy.saas.inquiry.pojo.excel.ImportReqDto;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author:chenxiaoyi
 * @Date:2025/04/18 9:47
 */
@Tag(name = "管理后台 - tenant-ExcelImport相关接口")
@RestController
@RequestMapping("/system/tenant/import")
public class TenantExcelImportController {

    @Resource
    private TenantExcelImportService tenantExcelImportService;

    @PostMapping("/batch-tenant-and-package-relation")
    @Operation(summary = "批量开通、编辑门店+开套餐")
    @PreAuthorize("@ss.hasPermission('tenant:tenant-and-package-relation:import')")
    @Lock4j(keys = "'batchTenantAndPackageRelation'", expire = 60000, failStrategy = ImportLockFailureStrategy.class)
    // @Idempotent(timeout = 30, keyResolver = ExpressionIdempotentKeyResolver.class, keyArg = "'batchTenantAndPackageRelation'", message = "请求繁忙,每次导入需间隔30秒再操作")
    public CommonResult<ImportResultDto> batchTenantAndPackageRelation(@RequestBody @Valid ImportReqDto importReqDto) {
        return success(tenantExcelImportService.batchTenantAndPackageRelation(importReqDto));
    }

    @PostMapping("/batch-update-package-relation")
    @Operation(summary = "批量修改订单信息")
    @PreAuthorize("@ss.hasPermission('tenant:batch-update-package-relation:import')")
    @Lock4j(keys = "'batchUpdatePackageRelation'", failStrategy = ImportLockFailureStrategy.class)
    // @Idempotent(timeout = 20, keyResolver = ExpressionIdempotentKeyResolver.class, keyArg = "'batchUpdatePackageRelation'", message = "请求繁忙,每次导入需间隔20秒再操作")
    public CommonResult<ImportResultDto> batchUpdatePackageRelation(@RequestBody @Valid ImportReqDto importReqDto) {
        return success(tenantExcelImportService.batchUpdatePackageRelation(importReqDto));
    }


    @PostMapping("/batch-open-package-relation")
    @Operation(summary = "批量续费套餐")
    @PreAuthorize("@ss.hasPermission('tenant:batch-open-package-relation:import')")
    @Lock4j(keys = "'batchOpenPackageRelation'", failStrategy = ImportLockFailureStrategy.class)
    // @Idempotent(timeout = 20, keyResolver = ExpressionIdempotentKeyResolver.class, keyArg = "'batchOpenPackageRelation'", message = "请求繁忙,每次导入需间隔20秒再操作")
    public CommonResult<ImportResultDto> batchOpenPackageRelation(@RequestBody @Valid ImportReqDto importReqDto) {
        return success(tenantExcelImportService.batchOpenPackageRelation(importReqDto));
    }

}
