package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack;


import com.xyy.saas.inquiry.pojo.TenantDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 门店-开通服务包关系 Response VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantServicePackRelationDetailRespVO {

    @Schema(description = "门店基础信息")
    private TenantDto tenantDto;

    @Schema(description = "医保服务包状态 0未开通 1开通")
    private Integer medicalInsuranceStatus;

    @Schema(description = "医保服务包")
    private List<TenantServicePackRelationItemVO> medicalInsurances;


    @Schema(description = "药监服务包状态 0未开通 1开通")
    private Integer drugSupervisionStatus;

    @Schema(description = "药监服务包")
    private List<TenantServicePackRelationItemVO> drugSupervisions;


    @Schema(description = "互联网医院监管服务包状态 0未开通 1开通")
    private Integer internetHospitalStatus;

    @Schema(description = "互联网医院监管服务包")
    private List<TenantServicePackRelationItemVO> internetHospitals;

    @Schema(description = "审方系统服务包状态 0未开通 1开通")
    private Integer thirdAuditStatus;

    @Schema(description = "审方系统")
    private List<TenantServicePackRelationItemVO> thirdAudits;

}