package com.xyy.saas.inquiry.user.server;

import cn.iocoder.yudao.framework.websocket.config.YudaoWebSocketAutoConfiguration;
import cn.iocoder.yudao.module.system.util.Jose4jTokenProperties;
import com.xyy.common.config.TomcatServerConfig;
import com.xyy.saas.inquiry.user.server.config.DubboAutoConfiguration;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@Slf4j
@SpringBootApplication(scanBasePackages = {"com.xyy.common", "cn.iocoder.yudao.module", "com.xyy.saas.inquiry"}
    , exclude = {YudaoWebSocketAutoConfiguration.class})
//@EnableDiscoveryClient
//@ComponentScan(excludeFilters = {
//        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = BannerApplicationRunner.class)
//})
@EnableDubbo(scanBasePackages = {"com.xyy.saas.inquiry"})
//@EnableEventBus
@Import({DubboAutoConfiguration.class, TomcatServerConfig.class})
@EnableConfigurationProperties(value = {Jose4jTokenProperties.class})
public class InquiryUserServerApplication {

    @Bean(initMethod = "")
    public TomcatServerConfig tomcatServerConfig() {
        TomcatServerConfig tomcatServerConfig = new TomcatServerConfig();
        String configUrl = "";
        try {
            configUrl = System.getProperty("user.dir") + "/tomcat_server.conf";
            if (configUrl.startsWith("file:")) {
                configUrl = configUrl.replace("file:", "");
            }
            File file = new File(configUrl);
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();
            log.info("文件创建成功:{}", configUrl);
        } catch (FileNotFoundException var2) {
            FileNotFoundException e = var2;
            log.error("获取路径异常：", e);
        } catch (IOException var3) {
            IOException e = var3;
            log.error("创建文件异常：", e);
        }
        TomcatServerConfig.configUrl = configUrl;
        return tomcatServerConfig;
    }

    public static void main(String[] args) {
        // 在Spring应用启动之前禁用Dubbo自动停机钩子和DubboDeployApplicationListener
        System.setProperty("dubbo.shutdown.hook", "false");
        System.setProperty("dubbo.application.listener.enabled", "false");
        log.info("Disabled Dubbo auto shutdown hook and DubboDeployApplicationListener in main method");
        
        // 设置skywalking服务名称
        System.setProperty("skywalking.agent.service_name", "inquiry-user-server");
        // 设置文件名编码格式，-Dsun.jnu.encoding=UTF-8 设置无效
        System.setProperty("sun.jnu.encoding", "UTF-8");

        SpringApplication app = new SpringApplication(InquiryUserServerApplication.class);
        if (isActuatorStartUp()) {
            int cacheSize = 10240;
            log.info("================= actuator start up with buffer: {} =================", cacheSize);
            app.setApplicationStartup(new BufferingApplicationStartup(cacheSize));
        }
        app.run(args);

        log.info("sun.jnu.encoding: {}", System.getProperty("sun.jnu.encoding"));
    }

    private static boolean isActuatorStartUp() {
        return "true".equalsIgnoreCase(System.getProperty("spring.actuator.startup.enable"));
    }
}