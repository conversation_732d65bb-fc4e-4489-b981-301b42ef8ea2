package com.xyy.saas.inquiry.transmitter.server.api.config;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.transmitter.api.config.TransmissionConfigApi;
import com.xyy.saas.inquiry.transmitter.api.servicepack.TransmissionServicePackApi;
import com.xyy.saas.inquiry.transmitter.api.config.dto.TransmissionConfigReqDTO;
import com.xyy.saas.inquiry.transmitter.api.servicepack.dto.TransmissionFunConfigOptionDTO;
import com.xyy.saas.inquiry.util.SpelParserUtil;
import jakarta.annotation.Resource;
import java.util.Optional;
import org.apache.dubbo.config.annotation.DubboService;

@DubboService
public class TransmissionConfigApiImpl implements TransmissionConfigApi {

    @Resource
    private TransmissionServicePackApi transmissionServicePackApi;

    @Override
    public boolean isProductChangeQueryCatalog(Long tenantId, Integer prescriptionType) {

        TransmissionFunConfigOptionDTO funConfigOptionDTO = getTransmissionConfigReqDTO(tenantId);

        return funConfigOptionDTO != null && SpelParserUtil.parseBoolean(funConfigOptionDTO.getProductChangeQueryCatalog(), prescriptionType);
    }


    @Override
    public Integer diagnosisChangeQueryCatalog(Long tenantId, Integer prescriptionType) {

        TransmissionFunConfigOptionDTO funConfigOptionDTO = getTransmissionConfigReqDTO(tenantId);

        if (funConfigOptionDTO != null && SpelParserUtil.parseBoolean(funConfigOptionDTO.getDiagnosisChangeQueryCatalog(), prescriptionType)) {
            return funConfigOptionDTO.getDiagnosisChangeQueryOrganId();
        }

        return null;
    }

    /**
     * 是否需要读取参保人信息
     *
     * @param tenantId
     * @param prescriptionType
     * @return
     */
    @Override
    public boolean isNeedReadInsuredInfo(Long tenantId, Integer prescriptionType) {
        TransmissionFunConfigOptionDTO funConfigOptionDTO = getTransmissionConfigReqDTO(tenantId);

        return funConfigOptionDTO != null && SpelParserUtil.parseBoolean(funConfigOptionDTO.getNeedReadInsuredInfo(), prescriptionType);
    }


    /**
     * 获取门店功能配置
     *
     * @param tenantId
     * @return
     */
    public TransmissionFunConfigOptionDTO getTransmissionConfigReqDTO(Long tenantId) {

        tenantId = Optional.ofNullable(tenantId).orElseGet(TenantContextHolder::getRequiredTenantId);

        TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder().tenantId(tenantId).nodeType(NodeTypeEnum.FUN_CONFIG).build();

        CommonResult<TransmissionFunConfigOptionDTO> commonResult = transmissionServicePackApi.selectConfigItem(configReqDTO, TransmissionFunConfigOptionDTO.class);

        if (commonResult.isSuccess() && commonResult.getData() != null) {
            return commonResult.getData();
        }

        return null;
    }
}
