package com.xyy.saas.inquiry.user.server.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ServiceConfig;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;
import org.apache.dubbo.config.bootstrap.builders.ServiceBuilder;
import org.apache.dubbo.rpc.model.ApplicationModel;
import org.springframework.aop.framework.Advised;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.aop.framework.ProxyFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;

import java.util.*;

import static org.springframework.util.ObjectUtils.nullSafeEquals;

/**
 * @Desc dubbo配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/9/10 下午5:17
 */
@Configuration
@Slf4j
public class DubboAutoConfiguration implements ApplicationListener<ContextRefreshedEvent>, ApplicationContextAware,
    Ordered {

    static {
        // 在类加载时就禁用Dubbo自动停机钩子，确保在Dubbo初始化之前生效
        System.setProperty("dubbo.shutdown.hook", "false");
        // 禁用Dubbo框架自带的DubboDeployApplicationListener，避免ExtensionDirector已销毁的错误
        System.setProperty("dubbo.application.listener.enabled", "false");
        log.info("Disabled Dubbo auto shutdown hook and DubboDeployApplicationListener at class loading time");
    }

    private ApplicationContext applicationContext;
    private ApplicationEventPublisher applicationEventPublisher;

    private final Set<Class<?>> serviceClasses = new HashSet<>();
    private final List<ServiceConfig> serviceConfigList = new ArrayList<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        this.applicationEventPublisher = applicationContext;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (nullSafeEquals(applicationContext, event.getSource())) {
            try {
                // 启动Dubbo服务，停机钩子已在静态代码块中禁用
                DubboBootstrap dubboBootstrap = DubboBootstrap.getInstance();
                dubboBootstrap.services(serviceConfigList);
                
                dubboBootstrap.start();
                log.info("Dubbo services started successfully with manual shutdown control!");
                
                // 发布Dubbo服务启动完成事件，用于触发Spring Cloud服务注册
                DubboServicesStartedEvent dubboStartedEvent = new DubboServicesStartedEvent(this);
                applicationEventPublisher.publishEvent(dubboStartedEvent);
                log.info("Published DubboServicesStartedEvent for Spring Cloud service registration");
            } catch (Exception e) {
                log.error("Dubbo services started failed: {}", e.getMessage(), e);
                // 根据需要决定是否重新抛出异常或采取其他措施
                throw e;
            }
        }
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE - 1;
    }

    @Bean
    public BeanPostProcessor dubboServiceBeanPostProcessor() {
        return new BeanPostProcessor() {
            @Override
            public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
                if (ServiceClassChecker.isApiImplClass(bean)) {
                    serviceClasses.add(bean.getClass());
                }
                return bean;
            }

            @Override
            public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
                registerAsDubboService(bean);
                return bean;
            }

            private void registerAsDubboService(Object bean) {
                final Object targetObject = ServiceClassChecker.getTargetObject(bean);
                if (targetObject == null || !serviceClasses.contains(targetObject.getClass())) {
                    return;
                }
                // // 确保获取到了ApplicationConfig
                // ApplicationConfig applicationConfig = ApplicationModel.getConfigManager().getApplication()
                //         .orElseThrow(() -> new IllegalStateException("No ApplicationConfig found."));
                // // 创建ServiceConfig实例
                // ServiceConfig<Object> service = new ServiceConfig<>();
                // service.setApplication(applicationConfig);
                // service.setRegistries(List.copyOf(ApplicationModel.getConfigManager().getRegistries()));
                // service.setProtocols(List.copyOf(ApplicationModel.getConfigManager().getProtocols())); // 多个协议可以用setProtocols()
                //
                // // 设置暴露的服务接口和服务实现
                Class<?> interfaceClass = targetObject.getClass().getInterfaces()[0];
                // service.setInterface(interfaceClass.getName()); // 设置服务接口名
                // service.setRef(targetObject); // 设置服务实现类实例
                // service.export();
                serviceConfigList.add(ServiceBuilder.newBuilder()
                    .interfaceClass(interfaceClass)
                    .ref(targetObject)
                    .build());
            }
        };
    }

    public static class ServiceClassChecker {

        public static boolean isApiImplClass(Object bean) {
            Object targetObject = getTargetObject(bean);
            if (Objects.isNull(targetObject)) {
                return false;
            }
            return targetObject.getClass().isAnnotationPresent(Service.class) && targetObject.getClass().getSimpleName().endsWith("ApiImpl");
        }

        public static Object getTargetObject(Object proxy) {
            if (!AopUtils.isAopProxy(proxy)) {
                return proxy;
            }
            return AopProxyUtils.getSingletonTarget(proxy);
//            return AopUtils.isJdkDynamicProxy(proxy) ? getJdkDynamicTargetObject(proxy) : getCglibTargetObject(proxy);
        }

        // 对于JDK动态代理
        private static Object getJdkDynamicTargetObject(Object proxy) {
            ProxyFactory pf = (ProxyFactory) proxy;
            return pf.getProxy();
        }

        // 假设proxy是由CGLIB创建的
        private static Object getCglibTargetObject(Object proxy) {
            try {
                return ((Advised) proxy).getTargetSource().getTarget();
            } catch (Exception e) {
                return proxy;
            }
        }
    }
}
