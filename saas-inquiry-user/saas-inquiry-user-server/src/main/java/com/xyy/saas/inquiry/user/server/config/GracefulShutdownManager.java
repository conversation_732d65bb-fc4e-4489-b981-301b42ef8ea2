package com.xyy.saas.inquiry.user.server.config;

import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 优雅停机管理器
 * 确保停机时先下线HTTP服务注册，再下线Dubbo服务注册
 * 
 * @Author: xucao
 * @DateTime: 2025/8/26 16:00
 * @Description: 优雅停机管理器
 */
@Slf4j
@Component
public class GracefulShutdownManager implements ApplicationListener<ContextClosedEvent> {

    private final AtomicBoolean shutdownInProgress = new AtomicBoolean(false);

    @Autowired
    private SpringCloudServiceRegistrationManager springCloudServiceRegistrationManager;

    /**
     * 监听Spring容器关闭事件，比@PreDestroy更早执行
     * 这样可以在Dubbo停机钩子执行之前先执行我们的优雅停机逻辑
     */
    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        if (shutdownInProgress.compareAndSet(false, true)) {
            log.info("[CONTEXT-CLOSED] Received ContextClosedEvent, starting graceful shutdown...");
            performGracefulShutdown();
        } else {
            log.info("[CONTEXT-CLOSED] Graceful shutdown already in progress, skipping...");
        }
    }

    /**
     * 优雅停机处理 - 作为备用方案
     * 如果ContextClosedEvent没有触发，@PreDestroy仍会执行
     */
    @PreDestroy
    public void gracefulShutdown() {
        if (shutdownInProgress.compareAndSet(false, true)) {
            log.info("[@PreDestroy] Starting graceful shutdown as fallback...");
            performGracefulShutdown();
        } else {
            log.info("[@PreDestroy] Graceful shutdown already completed, skipping...");
        }
    }

    /**
     * 执行优雅停机逻辑
     * 执行顺序：
     * 1. 先下线Spring Cloud HTTP服务注册，停止接收新的HTTP请求
     * 2. 等待一段时间，让负载均衡器感知到服务下线
     * 3. 再下线Dubbo RPC服务注册
     * 4. 等待现有请求处理完成
     */
    private void performGracefulShutdown() {
        long startTime = System.currentTimeMillis();
        log.info("=== Starting graceful shutdown process at {} ===", new java.util.Date());
        
        try {
            // 第一步：下线Spring Cloud HTTP服务注册
            log.info("[SHUTDOWN-STEP-1] Deregistering Spring Cloud HTTP service from Nacos...");
            long step1Start = System.currentTimeMillis();
            
            springCloudServiceRegistrationManager.deregisterService();
            
            long step1End = System.currentTimeMillis();
            log.info("[SHUTDOWN-STEP-1] Spring Cloud HTTP service deregistered successfully in {}ms", step1End - step1Start);
            
            // 等待负载均衡器感知服务下线，避免新的HTTP请求进入
            log.info("[SHUTDOWN-STEP-2] Waiting 10 seconds for load balancer to detect service deregistration...");
            for (int i = 10; i > 0; i--) {
                log.info("[SHUTDOWN-STEP-2] Countdown: {} seconds remaining...", i);
                TimeUnit.SECONDS.sleep(1);
            }
            log.info("[SHUTDOWN-STEP-2] Load balancer detection wait completed");
            
            // 第二步：下线Dubbo RPC服务注册
            log.info("[SHUTDOWN-STEP-3] Shutting down Dubbo services...");
            long step3Start = System.currentTimeMillis();
            
            try {
                DubboBootstrap dubboBootstrap = DubboBootstrap.getInstance();
                if (dubboBootstrap.isStarted()) {
                    log.info("[SHUTDOWN-STEP-3] Dubbo bootstrap is started, proceeding with shutdown...");
                    dubboBootstrap.stop();
                    long step3End = System.currentTimeMillis();
                    log.info("[SHUTDOWN-STEP-3] Dubbo services shutdown successfully in {}ms", step3End - step3Start);
                } else {
                    log.info("[SHUTDOWN-STEP-3] Dubbo services were not started, skipping Dubbo shutdown");
                }
            } catch (IllegalStateException e) {
                if (e.getMessage() != null && e.getMessage().contains("ExtensionDirector is destroyed")) {
                    log.info("[SHUTDOWN-STEP-3] Dubbo has already been stopped by shutdown hook, skipping manual stop");
                } else {
                    log.warn("[SHUTDOWN-STEP-3] Error checking Dubbo status: {}", e.getMessage());
                }
            } catch (Exception e) {
                log.warn("[SHUTDOWN-STEP-3] Unexpected error during Dubbo shutdown: {}", e.getMessage(), e);
            }
            
            // 第三步：等待现有请求处理完成
            log.info("[SHUTDOWN-STEP-4] Waiting 5 seconds for existing requests to complete...");
            for (int i = 5; i > 0; i--) {
                log.info("[SHUTDOWN-STEP-4] Countdown: {} seconds remaining...", i);
                TimeUnit.SECONDS.sleep(1);
            }
            log.info("[SHUTDOWN-STEP-4] Request completion wait finished");
            
            long totalTime = System.currentTimeMillis() - startTime;
            log.info("=== Graceful shutdown process completed successfully in {}ms ===", totalTime);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            long totalTime = System.currentTimeMillis() - startTime;
            log.warn("=== Graceful shutdown process was interrupted after {}ms: {} ===", totalTime, e.getMessage());
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("=== Error occurred during graceful shutdown after {}ms: {} ===", totalTime, e.getMessage(), e);
        }
    }
    
    /**
     * 获取当前服务注册状态
     * 
     * @return 服务注册状态信息
     */
    public String getServiceStatus() {
        boolean httpRegistered = springCloudServiceRegistrationManager.isRegistered();
        boolean dubboStarted = DubboBootstrap.getInstance().isStarted();
        
        return String.format("HTTP Service Registered: %s, Dubbo Service Started: %s", 
                            httpRegistered, dubboStarted);
    }
}