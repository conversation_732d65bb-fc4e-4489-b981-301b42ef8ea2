package com.xyy.saas.inquiry.user.server.config;

import com.alibaba.cloud.nacos.registry.NacosRegistration;
import com.alibaba.cloud.nacos.registry.NacosServiceRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * Spring Cloud服务注册管理器
 * 监听DubboServicesStartedEvent事件，在Dubbo服务完全启动后手动注册Spring Cloud服务到Nacos
 * 
 * @Author: xucao
 * @DateTime: 2025/8/26 15:35
 * @Description: 手动注册Spring Cloud服务到Nacos
 */
@Component
@Slf4j
public class SpringCloudServiceRegistrationManager implements ApplicationListener<DubboServicesStartedEvent> {

    @Autowired
    private NacosServiceRegistry nacosServiceRegistry;

    @Autowired
    private NacosRegistration registration;

    private volatile boolean isRegistered = false;
    private int webServerPort = -1;

    /**
     * 监听Web服务器初始化事件，获取服务器端口
     */
    @org.springframework.context.event.EventListener
    public void onWebServerInitialized(WebServerInitializedEvent event) {
        this.webServerPort = event.getWebServer().getPort();
        log.info("Web server initialized with port: {}", webServerPort);
    }

    /**
     * 监听Dubbo服务启动完成事件，执行Spring Cloud服务注册
     */
    @Override
    public void onApplicationEvent(DubboServicesStartedEvent event) {
        if (isRegistered) {
            log.info("Spring Cloud service already registered, skipping duplicate registration");
            return;
        }

        try {
            log.info("Received DubboServicesStartedEvent, starting Spring Cloud service registration...");
            
            // 等待Web服务器端口可用
            if (webServerPort == -1) {
                log.warn("Web server port not available yet, waiting for WebServerInitializedEvent...");
                // 可以考虑添加重试机制或者等待逻辑
                return;
            }

            // 设置正确的端口
            registration.setPort(webServerPort);
            
            log.info("Registering Spring Cloud service to Nacos with port: {}", webServerPort);
            nacosServiceRegistry.register(registration);
            
            isRegistered = true;
            log.info("Spring Cloud service registered successfully to Nacos after Dubbo services startup");
            
        } catch (Exception e) {
            log.error("Failed to register Spring Cloud service to Nacos: {}", e.getMessage(), e);
            // 可以考虑添加重试机制
        }
    }

    /**
     * 手动下线Spring Cloud服务
     * 用于优雅停机时主动下线服务注册
     */
    public void deregisterService() {
        if (isRegistered) {
            try {
                log.info("Manually deregistering Spring Cloud service from Nacos...");
                nacosServiceRegistry.deregister(registration);
                isRegistered = false;
                log.info("Spring Cloud service deregistered successfully");
            } catch (Exception e) {
                log.error("Failed to deregister Spring Cloud service: {}", e.getMessage(), e);
            }
        } else {
            log.info("Spring Cloud service was not registered, skipping deregistration");
        }
    }
    
    /**
     * 获取注册状态
     * 
     * @return 是否已注册
     */
    public boolean isRegistered() {
        return isRegistered;
    }
}