package com.xyy.saas.inquiry.user.server.config;

import org.springframework.context.ApplicationEvent;

/**
 * Dubbo服务启动完成事件
 * 当所有Dubbo服务完全启动后发布此事件，用于触发Spring Cloud服务注册
 * 
 * @Author: xucao
 * @DateTime: 2025/8/26 15:30
 * @Description: Dubbo服务启动完成事件
 */
public class DubboServicesStartedEvent extends ApplicationEvent {

    /**
     * 构造函数
     * 
     * @param source 事件源对象
     */
    public DubboServicesStartedEvent(Object source) {
        super(source);
    }
    
    /**
     * 获取事件描述
     * 
     * @return 事件描述信息
     */
    public String getEventDescription() {
        return "Dubbo services have been fully started and are ready for service registration";
    }
}