package com.xyy.saas.inquiry.patient.service.inquiry.impl;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_NOT_MATCH_HOSPITAL;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_RECORD_NOT_EXISTS;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.THIRD_PARTY_PRE_INQUIRY_SAVE_FAIL;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.THIRD_PARTY_REPETITION_INQUIRY;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import cn.iocoder.yudao.framework.ratelimiter.core.keyresolver.impl.ExpressionRateLimiterKeyResolver;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.config.gray.EsPrescriptionGrayConfig;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.constant.PrescriptionConstant;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.ding.DingService;
import com.xyy.saas.inquiry.ding.DingService.Markdown;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantDeductCostDto;
import com.xyy.saas.inquiry.drugstore.api.user.UserApi;
import com.xyy.saas.inquiry.enums.common.GrayStrategyEnum;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryDispatchProcess;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum.InquiryGroupStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionDateTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.QuerySourceEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalDataConfigApi;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalEmployeeApi;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.im.api.message.InquiryImMessageApi;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryLastMessageDto;
import com.xyy.saas.inquiry.im.api.user.InquiryImUserApi;
import com.xyy.saas.inquiry.mq.inquiry.InquiryRecordCreateEvent;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryCreateMessage;
import com.xyy.saas.inquiry.mq.tenant.cost.InquiryReBackCostEvent;
import com.xyy.saas.inquiry.mq.tenant.cost.InquiryReBackProducer;
import com.xyy.saas.inquiry.mq.tenant.cost.TenantChangeCostDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordGrabbingDoctorDto;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordDetailRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordSaveReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryBoardRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryQueueingRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryRespVO;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordConvert;
import com.xyy.saas.inquiry.patient.convert.third.ThirdPartyPreInquiryConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordDetailMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import com.xyy.saas.inquiry.patient.dal.redis.inquiry.InquiryRedisDao;
import com.xyy.saas.inquiry.patient.dal.redis.tenant.TenantRedisDao;
import com.xyy.saas.inquiry.patient.es.inquiry.InquiryRecordEsService;
import com.xyy.saas.inquiry.patient.mq.producer.InquiryRecordProducer;
import com.xyy.saas.inquiry.patient.service.dispatch.InquiryBaseDispatch;
import com.xyy.saas.inquiry.patient.service.dispatch.InquiryPreCheck;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryImService;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryService;
import com.xyy.saas.inquiry.patient.service.inquiry.handle.InquiryCancelHandle;
import com.xyy.saas.inquiry.patient.service.patient.InquiryPatientInfoService;
import com.xyy.saas.inquiry.patient.service.third.ThirdPartyService;
import com.xyy.saas.inquiry.patient.util.BusinessUtil;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.hospital.InquiryHosDataConfigQueryParamDto;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

/**
 * @Desc 问诊服务层
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/28 下午2:32
 */
@Service
@Validated
@Slf4j
@RefreshScope
public class InquiryServiceImpl implements InquiryService {

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;

    @Resource
    private InquiryRecordDetailMapper inquiryRecordDetailMapper;

    @Resource
    private InquiryPatientInfoService inquiryPatientInfoService;

    @Resource
    private InquiryHospitalApi inquiryHospitalApi;

    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    @DubboReference
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @DubboReference
    private InquiryImUserApi inquiryImUserApi;

    @DubboReference
    private InquiryImMessageApi inquiryImMessageApi;

    @Resource
    private TenantRedisDao tenantRedisDao;

    @Resource
    private InquiryRedisDao inquiryRedisDao;

    @DubboReference
    private TenantPackageCostApi tenantPackageCostApi;

    @Resource
    private InquiryReBackProducer inquiryReBackProducer;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private UserApi userApi;

    @Resource
    private InquiryRecordProducer inquiryRecordProducer;

    @Resource
    private ConfigApi configApi;

    @Autowired
    private Map<String, InquiryBaseDispatch> inquiryDispatchMap;

    @Autowired
    @Lazy
    private InquiryPreCheck inquiryPreCheck;

    @Resource
    private LockTemplate lockTemplate;

    @Resource
    private InquiryImService inquiryImService;

    @DubboReference
    private TenantParamConfigApi tenantParamConfigApi;

    @Resource
    private ThirdPartyService thirdPartyService;

    @Resource
    private DingService dingService;

    @Resource
    private InquiryCancelHandle inquiryCancelHandle;

    @DubboReference
    private InquiryHospitalEmployeeApi inquiryHospitalEmployeeApi;

    @DubboReference
    private InquiryHospitalDataConfigApi inquiryHospitalDataConfigApi;

    @Resource
    private InquiryRecordEsService inquiryRecordEsService;

    @Resource
    private EsPrescriptionGrayConfig esPrescriptionGrayConfig;

    /**
     * 刷新问诊imHistory字段开关
     */
    @Value("${flush.imHistory.switch:true}")
    private Boolean flushImHistorySwitch;

    /**
     * 刷新问诊imHistory字段开关
     */
    @Value("${flush.imHistory.interval.time:1000}")
    private Long flushImHistoryIntervalTime;

    @PostConstruct
    public void afterPostConstruct() {
        for (InquiryDispatchProcess process : InquiryDispatchProcess.values()) {
            InquiryBaseDispatch dispatch = inquiryDispatchMap.get(process.getNode());
            if (null == dispatch) {
                continue;
            }
            if (StringUtils.isBlank(process.getNext())) {
                continue;
            }
            dispatch.setNextNode(inquiryDispatchMap.get(process.getNext()));
        }
    }


    @Override
    public CommonResult<?> drugstoreInquiryPreCheck(DrugstoreInquiryReqVO drugstoreInquiryReqVO) {
        TenantDto tenantDto = tenantApi.getTenant();

        InquiryRecordDto inquiryDto = InquiryRecordConvert.INSTANCE.convertPreCheck(drugstoreInquiryReqVO);

        inquiryPreCheck.checkParam(inquiryDto, tenantDto);
        return CommonResult.success(null);
    }

    /**
     * app 去问诊
     *
     * @return 问诊编码
     */
    @Override
    @Idempotent(message = "问诊已发起，请勿重复提交") // 幂等、防止重复提交
    @RateLimiter(keyArg = "'create-inquiry-record'", keyResolver = ExpressionRateLimiterKeyResolver.class, message = "问诊频率过高，请稍后再试")
    public CommonResult<InquiryRespVO> createInquiryRecord(DrugstoreInquiryReqVO drugstoreInquiryReqVO) {
        // 小程序问诊检查
        CommonResult<InquiryRespVO> checkResult = checkWeChatInquiry(drugstoreInquiryReqVO);
        if (checkResult != null) {
            return checkResult;
        }
        InquiryRecordDto inquiryDto = InquiryRecordConvert.INSTANCE.convert(drugstoreInquiryReqVO);
        // 扣问诊额度的乐观锁update
        TenantDeductCostDto tenantDeductCostDto = tenantPackageCostApi.deductTenantCost(InquiryWayTypeEnum.fromCode(drugstoreInquiryReqVO.getInquiryWayType()), inquiryDto.getPref(),
            inquiryDto.getInquiryRecordDetailDto().getPrescriptionType());
        log.info("问诊扣额度完成,问诊单号：{} ,返回内容：{}", inquiryDto.getPref(), JSON.toJSONString(tenantDeductCostDto));
        // 判断是否匹配到可就医的医院
        if (CollectionUtils.isEmpty(tenantDeductCostDto.getHospitalPrefs())) {
            TenantDto tenantDto = TenantContextHolder.getTenantContextInfo(TenantConstant.TENANT_CONTEXT_KEY_TENANT_DTO);
            dingService.send(Markdown.title("问诊未匹配到可接诊医院").add("问诊门店", tenantDto.getName()).add("操作用户", LoginUserContextUtils.getLoginUserId()));
            throw exception(INQUIRY_NOT_MATCH_HOSPITAL);
        }
        // 发起问诊
        return gotoInquiry(inquiryDto, tenantDeductCostDto);
    }

    /**
     * 小程序问诊检查
     *
     * @param drugstoreInquiryReqVO
     * @return
     */
    private CommonResult<InquiryRespVO> checkWeChatInquiry(DrugstoreInquiryReqVO drugstoreInquiryReqVO) {
        // 非小程序问诊，直接返回
        if (ObjectUtil.notEqual(ClientChannelTypeEnum.MINI_PROGRAM.getCode(), drugstoreInquiryReqVO.getClientChannelType()) || ObjectUtil.isNotEmpty(drugstoreInquiryReqVO.getBaseInquiryReqVO().getThirdPartyPreInquiryId())) {
            return null;
        }
        // 查询门店小程序问诊配置
        Integer optionConfig = tenantParamConfigApi.getParamConfigValueSelf2SysInteger(TenantParamConfigTypeEnum.INQUIRY_PRES_AUDIT_SWITCH);
        if (ObjectUtil.notEqual(CommonStatusEnum.ENABLE.getStatus(), optionConfig)) {
            return null;
        }
        CommonResult<ThirdPartyPreInquiryRespVO> result = thirdPartyService.preInquiry(ThirdPartyPreInquiryConvert.INSTANCE.convertInquiryVO2PreInquiryVO(drugstoreInquiryReqVO));
        if (result.isError()) {
            throw exception(THIRD_PARTY_PRE_INQUIRY_SAVE_FAIL, result.getMsg());
        }
        return CommonResult.success(InquiryRespVO.builder().requireAudit(Boolean.TRUE).preInquiryId(result.getData().getPreInquiryId()).preInquiryPref(result.getData().getPreInquiryPref()).build());
    }

    /**
     * 问诊发起核心流程
     *
     * @param inquiryDto          问诊单信息 问诊请求参数 {@link InquiryPreCheck} 问诊调度责任链入口
     * @param tenantDeductCostDto 扣费信息
     * @return
     */
    private CommonResult<InquiryRespVO> gotoInquiry(InquiryRecordDto inquiryDto, TenantDeductCostDto tenantDeductCostDto) {
        LockInfo lockInfo = null;
        try {
            if (inquiryDto.getThirdPartyPreInquiryId() != null) {
                // 三方渠道问诊加锁。acquire-timeout 可以理解为排队获取锁的时长。expire 锁过期时间,主要是防止死锁。
                lockInfo = lockTemplate.lock(RedisKeyConstants.getThirdPartyPreInquiryLockKey(inquiryDto.getThirdPartyPreInquiryId()), 30000L, 5000L);
                if (null == lockInfo) {
                    throw exception(THIRD_PARTY_REPETITION_INQUIRY);
                }
            }
            // 执行问诊调度链
            inquiryDispatchMap.get(InquiryDispatchProcess.PRE_CHECK_PROCESS.getNode()).doExecute(inquiryDto.setChoiceHospitalList(tenantDeductCostDto.getHospitalPrefs()));
            // 发送调度派单MQ
            inquiryRecordProducer.sendMessage(InquiryRecordCreateEvent.builder().msg(InquiryCreateMessage.builder().inquiryPref(inquiryDto.getPref()).hospitalDeptDto(inquiryDto.getHospitalDeptDto()).build()).build());
            return CommonResult.success(InquiryRespVO.builder().inquiryPref(inquiryDto.getPref()).autoInquiry(inquiryDto.getAutoInquiry()).build());
        } catch (RuntimeException runtimeException) {
            log.error("问诊异常", runtimeException);
            // 发送mq 加回额度
            inquiryReBackProducer.sendMessage(
                InquiryReBackCostEvent.builder().msg(TenantChangeCostDto.builder().bizId(inquiryDto.getPref()).recordType(CostRecordTypeEnum.INQUIRY.getCode()).reBackRecordType(CostRecordTypeEnum.INQUIRY_CANAL.getCode()).build()).build());
            if (runtimeException instanceof ServiceException) {
                return CommonResult.error(((ServiceException) runtimeException).getCode(), runtimeException.getMessage());
            }
            return CommonResult.error(runtimeException.getMessage());
        } finally {
            // 释放锁
            lockTemplate.releaseLock(lockInfo);
        }
    }

    /**
     * 取消问诊
     *
     * @param inquiryPref 问诊单号
     * @return 取消结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelInquiryRecord(String inquiryPref) {
        InquiryRecordDO inquiryDO = inquiryRecordMapper.selectOne(InquiryRecordDO::getPref, inquiryPref);
        if (inquiryDO == null) {
            throw exception(INQUIRY_RECORD_NOT_EXISTS);
        }
        return inquiryCancelHandle.executeCancelLogic(inquiryDO.getInquiryBizType(), inquiryDO);
    }


    /**
     * admin 运营平台更新问诊单（上线前删除）
     *
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateInquiryRecord(InquiryRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryRecordExists(updateReqVO.getId());
        // 更新
        InquiryRecordDO updateObj = BeanUtils.toBean(updateReqVO, InquiryRecordDO.class);
        inquiryRecordMapper.updateById(updateObj);
    }

    /**
     * admin 运营平台删除问诊单（上线前删除）
     *
     * @param id 编号
     */
    @Override
    public void deleteInquiryRecord(Long id) {
        // 校验存在
        validateInquiryRecordExists(id);
        // 删除
        inquiryRecordMapper.deleteById(id);
    }

    /**
     * 校验问诊单存在
     *
     * @param id 问诊id
     */
    private InquiryRecordDO validateInquiryRecordExists(Long id) {
        InquiryRecordDO inquiryRecordDO = inquiryRecordMapper.selectById(id);
        if (inquiryRecordDO == null) {
            throw exception(INQUIRY_RECORD_NOT_EXISTS);
        }
        return inquiryRecordDO;
    }

    private InquiryRecordDO validateInquiryRecordExists(String inquiryPref) {
        InquiryRecordDO inquiryRecordDO = inquiryRecordMapper.selectOne(InquiryRecordDO::getPref, inquiryPref);
        if (inquiryRecordDO == null) {
            throw exception(INQUIRY_RECORD_NOT_EXISTS);
        }
        return inquiryRecordDO;
    }

    @Override
    public DrugstoreInquiryBoardRespVO getDrugstoreBoard() {
        // 1、获取租户信息
        TenantDto tenantDto = tenantApi.getTenant();
        List<String> inquiryList = tenantRedisDao.getDrugstoreInquiryList(tenantDto.getId(), tenantDto.getEnvTag());
        List<String> currentInquiryList = tenantRedisDao.getDrugstoreCurrentInquiryList(tenantDto.getId(), tenantDto.getEnvTag());
        return DrugstoreInquiryBoardRespVO.builder()
            .waitReceptionNum(checkStatusAndReturnBoardNum(inquiryList, tenantDto, InquiryStatusEnum.QUEUING.getStatusCode()))
            .receivingNum(checkStatusAndReturnBoardNum(currentInquiryList, tenantDto, InquiryStatusEnum.INQUIRING.getStatusCode()))
            .build();
    }

    /**
     * 检查问诊单状态并返回问诊看板数量
     *
     * @param inquiryList
     * @param inquiryStatus
     * @param tenantDto
     * @return
     */
    private Integer checkStatusAndReturnBoardNum(List<String> inquiryList, TenantDto tenantDto, Integer inquiryStatus) {
        if (CollectionUtils.isEmpty(inquiryList)) {
            return 0;
        }
        List<InquiryRecordDO> recordList = inquiryRecordMapper.selectListByCondition(InquiryRecordPageReqVO.builder().tenantId(tenantDto.getId()).prefs(inquiryList).build());
        List<String> result = new ArrayList<>();
        Map<String, InquiryRecordDO> inquiryMap = recordList.stream().collect(Collectors.toMap(InquiryRecordDO::getPref, Function.identity(), (k1, k2) -> k2));
        inquiryList.forEach(pref -> {
            InquiryRecordDO inquiryRecordDO = inquiryMap.get(pref);
            // 为空或者状态不对
            if (ObjectUtil.isEmpty(inquiryRecordDO) || ObjectUtil.notEqual(inquiryRecordDO.getInquiryStatus(), inquiryStatus)) {
                // 从当前门店的问诊缓存中移除当前问诊单
                String key = ObjectUtil.equals(inquiryStatus, InquiryStatusEnum.QUEUING.getStatusCode()) ? RedisKeyConstants.getDrugstoreInquiryKey(tenantDto.getId(), tenantDto.getEnvTag()) :
                    RedisKeyConstants.getDrugstoreCurrentInquiryKey(tenantDto.getId(), tenantDto.getEnvTag());
                tenantRedisDao.removeInquiryForDrugstore(key, pref);
                return;
            }
            result.add(pref);
        });
        return result.size();
    }


    /**
     * 查询问诊问题以及答案
     *
     * @return
     */
    @Override
    public Map getInquiryQuestionAnswer() {
        return JSON.parseObject(configApi.getConfigValueByKey(InquiryConstant.INQUIRY_QUESTION_ANSWER), Map.class);
    }

    @Override
    @InquiryDateType
    public InquiryRecordDetailRespVO getInquiryRecord(Long id) {
        InquiryRecordDO inquiryRecordDO = validateInquiryRecordExists(id);
        return getInquiryRecordDetailVO(inquiryRecordDO.getPref());
    }

    public InquiryRecordDetailRespVO getInquiryRecordDetailVO(String pref) {
        InquiryRecordDO recordDO = getInquiryByPref(pref);
        if (recordDO == null) {
            throw exception(INQUIRY_RECORD_NOT_EXISTS);
        }
        // 填充基础信息
        InquiryRecordDetailRespVO detailRespVO = getInquiryRecordDetailRespVO(recordDO);
        return detailRespVO;
    }


    /**
     * 填充明细信息
     *
     * @param recordDO
     * @return
     */
    public InquiryRecordDetailRespVO getInquiryRecordDetailRespVO(InquiryRecordDO recordDO) {
        // 查询当前问诊租户信息
        TenantDto tenantDto = tenantApi.getTenant(recordDO.getTenantId());
        int timeOut = MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(PrescriptionConstant.PRESCRIPTION_ISSUE_TIMEOUT), 30);
        InquiryRecordDetailRespVO respVO = InquiryRecordConvert.INSTANCE.convertDO2VOWithRemainingTime(recordDO, timeOut, tenantDto);
        // 填充明细信息
        Optional.ofNullable(inquiryRecordDetailMapper.selectByRecordPref(recordDO.getPref())).ifPresent(detailDO -> InquiryRecordConvert.INSTANCE.convertDetailDO2VO(detailDO, respVO));
        return respVO;
    }

    /**
     * 更新问诊单结束时间
     *
     * @param inquiryRecordDto
     * @return
     */
    @Override
    public boolean updateInquiryEndTime(InquiryRecordDto inquiryRecordDto) {
        return inquiryRecordMapper.updateInquiryEndTime(inquiryRecordDto) > 0;
    }

    @Override
    public InquiryRecordDto getInquiryDtoByInquiryPref(String inquiryPref) {
        InquiryRecordDO inquiryRecordDO = inquiryRecordMapper.selectByPrefForceMaster(inquiryPref);
        if (inquiryRecordDO == null) {
            return null;
        }
        return InquiryRecordConvert.INSTANCE.convertDO2DTO(inquiryRecordDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInquirys(List<InquiryRecordDto> updateRecords) {

        if (CollUtil.isEmpty(updateRecords)) {
            return;
        }

        List<InquiryRecordDO> inquiryRecordDOS = InquiryRecordConvert.INSTANCE.convertDTOList2DOList(updateRecords);
        for (List<InquiryRecordDO> recordDOS : Lists.partition(inquiryRecordDOS, 50)) {
            inquiryRecordMapper.updateBatch(recordDOS);
        }
    }

    /**
     * 获得问诊记录
     *
     * @param pref 问诊单号
     * @return 问诊记录
     */
    @Override
    public InquiryRecordDO getInquiryByPref(String pref) {
        return inquiryRecordMapper.selectOne(InquiryRecordDO::getPref, pref);
    }

    /**
     * 获得问诊记录
     *
     * @param pref 问诊单号
     * @return 问诊记录
     */
    @Override
    public InquiryRecordDto getInquiryDtoByPref(String pref) {
        InquiryRecordDO inquiryRecordDO = inquiryRecordMapper.selectOne(InquiryRecordDO::getPref, pref);
        if (inquiryRecordDO == null) {
            throw exception(INQUIRY_RECORD_NOT_EXISTS);
        }
        return InquiryRecordConvert.INSTANCE.convertDO2DTO(inquiryRecordDO);
    }

    public List<InquiryRecordDto> getInquiryDtoByPrefs(List<String> inquiryPrefs) {
        if (CollUtil.isEmpty(inquiryPrefs)) {
            return List.of();
        }
        List<InquiryRecordDO> inquiryRecordDOs = inquiryRecordMapper.selectList(InquiryRecordDO::getPref, inquiryPrefs);
        return InquiryRecordConvert.INSTANCE.convertDOS2DTOS(inquiryRecordDOs);
    }


    @Override
    public InquiryRecordDetailDO getInquiryRecordDetailByPref(String pref) {
        return inquiryRecordDetailMapper.selectByRecordPref(pref);
    }

    @Override
    public List<InquiryRecordDetailDO> getInquiryRecordDetailByPrefs(List<String> prefs) {
        if (CollUtil.isEmpty(prefs)) {
            return List.of();
        }
        return inquiryRecordDetailMapper.selectList(InquiryRecordDetailDO::getInquiryPref, prefs);
    }

    @Override
    @InquiryDateType
    public PageResult<InquiryRecordRespVO> getInquiryRecordPage(InquiryRecordPageReqVO pageReqVO) {
        // 这里需要根据 pageReqVO.querySource 来判断查询来源，根据查询来源组装不同的查询参数
        handlerParam(pageReqVO);
        if (pageReqVO.isHospitalEmployee() && CollectionUtils.isEmpty(pageReqVO.getHospitalPrefs())) {
            return PageResult.empty();
        }
        pageReqVO.setInquiryStatusList(InquiryGroupStatusEnum.getInquiryStatusList(pageReqVO.getInquiryGroupStatus()));

        PageResult<InquiryRecordRespVO> pageResult;
        // web查询判断灰度走ES
        if (Objects.equals(pageReqVO.getQuerySource(), QuerySourceEnum.WEB)) {

            if (GrayStrategyEnum.isGrayTenant(esPrescriptionGrayConfig.getGrayConfig())) {
                pageResult = inquiryRecordEsService.getEsInquiryRecordPage(pageReqVO);
            } else {
                pageResult = InquiryRecordConvert.INSTANCE.convertPage(inquiryRecordMapper.selectPage(pageReqVO));
            }
        } else {
            pageResult = InquiryRecordConvert.INSTANCE.convertPage(inquiryRecordMapper.selectPage(pageReqVO));
        }
        if (CollUtil.isEmpty(pageResult.getList())) {
            return pageResult;
        }
        // 填充详情
        Map<String, InquiryRecordDetailDO> recordDetailDOMap = inquiryRecordDetailMapper.selectList(InquiryRecordDetailDO::getInquiryPref, pageResult.getList().stream().map(InquiryRecordRespVO::getPref).toList()).stream()
            .collect(Collectors.toMap(InquiryRecordDetailDO::getInquiryPref, Function.identity(), (k1, k2) -> k2));
        Optional.ofNullable(pageResult.getList()).orElse(List.of()).forEach(inquiryRecordRespVO -> {
            inquiryRecordRespVO.setInquiryGroupStatus(InquiryStatusEnum.getInquiryGroupStatus(inquiryRecordRespVO.getInquiryStatus()));
            inquiryRecordRespVO.setDateType(pageReqVO.getDateType());
            InquiryRecordDetailDO inquiryRecordDetailDO = recordDetailDOMap.get(inquiryRecordRespVO.getPref());
            if (inquiryRecordDetailDO != null) {
                inquiryRecordRespVO.setPrescriptionType(inquiryRecordDetailDO.getPrescriptionType());
            }
        });
        return pageResult;
    }

    @Override
    public List<InquiryRecordDO> getInquiryRecordList(InquiryRecordPageReqVO pageReqVO) {
        return inquiryRecordMapper.selectListByCondition(pageReqVO);
    }

    /**
     * 批量填充问诊聊天记录
     *
     * @param page 分页问诊记录
     */
    @Override
    public void assembleMessage(PageResult<InquiryRecordRespVO> page) {
        if (CollectionUtils.isEmpty(page.getList())) {
            return;
        }
        // 根据问诊单号集合，查询当前问诊最后一条聊天记录
        List<InquiryLastMessageDto> lastMessageDtos = inquiryImMessageApi.getLastMessage(page.getList().stream().map(InquiryRecordRespVO::getPref).toList());
        if (CollectionUtils.isEmpty(lastMessageDtos)) {
            return;
        }
        Map<String, String> lastMessageMap = lastMessageDtos.stream().filter(dto -> dto != null && dto.getInquiryPref() != null && dto.getLastMsg() != null)
            .collect(Collectors.toMap(InquiryLastMessageDto::getInquiryPref, InquiryLastMessageDto::getLastMsg, (k1, k2) -> k2));
        // 未读消息数，生成 1 到 3 之间的随机数
        page.getList().forEach(res -> {
            res.setLastMsg(lastMessageMap.getOrDefault(res.getPref(), ""));
            res.setUnReadCount(0);
        });
    }

    /**
     * 根据问诊单号，更新IM聊天PDF文件URL地址，以及im聊天记录
     *
     * @return true or false
     */
    @Override
    public Boolean updateInquiry(InquiryRecordDto inquiryRecordDto) {
        // 校验存在
        validateInquiryRecordExists(inquiryRecordDto.getId());
        InquiryRecordDO inquiryRecordDO = InquiryRecordConvert.INSTANCE.convertDTO2DO(inquiryRecordDto);
        return inquiryRecordMapper.updateById(inquiryRecordDO) > 0;
    }


    /**
     * 医生抢单接诊乐观锁
     *
     * @param inquiryRecordGrabbingDoctorDto 医生接诊dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean doctorGrabbingInquiry(InquiryRecordGrabbingDoctorDto inquiryRecordGrabbingDoctorDto) {
        Boolean result = inquiryRecordMapper.doctorGrabbingInquiry(inquiryRecordGrabbingDoctorDto) > 0;
        InquiryRecordDO inquiryRecordDO = inquiryRecordMapper.selectOne(InquiryRecordDO::getId, inquiryRecordGrabbingDoctorDto.getId());
        // 未抢单成功 或 非图文问诊 ，直接返回抢单结果
        if (!result || ObjectUtil.notEqual(inquiryRecordDO.getInquiryWayType(), InquiryWayTypeEnum.TEXT.getCode())) {
            return result;
        }
        // 图文问诊，需要生成医患沟通（医生小助）对话记录 以及 用药申请单 接诊医生卡片信息
        inquiryImService.generateAutomaticChatRecord(inquiryRecordDO);
        return result;
    }


    /**
     * 获取问诊单排队信息
     *
     * @param inquiryPref 问诊单号
     * @return 排队信息
     */
    @Override
    public InquiryQueueingRespVO getInquriyQueueing(String inquiryPref) {
        // 查询问诊单信息
        InquiryRecordDO inquiryDO = inquiryRecordMapper.selectOne(InquiryRecordDO::getPref, inquiryPref);
        if (inquiryDO == null) {
            throw exception(INQUIRY_RECORD_NOT_EXISTS);
        }
        // 医生IM账号
        String doctorIm = null;
        //  当前问诊是否已接诊
        if (inquiryDO.getInquiryStatus() >= InquiryStatusEnum.INQUIRING.getStatusCode() && StringUtils.isNotBlank(inquiryDO.getDoctorPref())) {
            // 查询当前医生IM信息
            doctorIm = inquiryImUserApi.getDoctorImAccountByDoctorPref(inquiryDO.getDoctorPref());
        }
        // 组装返回参数
        InquiryQueueingRespVO respVO = InquiryRecordConvert.INSTANCE.convertDO2QueueingVO(doctorIm, inquiryDO);
        // 获取接诊大厅队列信息
        List<String> receptionList = inquiryRedisDao.getInquiryReceptionList(RedisKeyConstants.getRecetionAreaKey(inquiryDO.getHospitalPref(), inquiryDO.getDeptPref(), inquiryDO.getAutoInquiry(), inquiryDO.getInquiryWayType()));
        if (CollectionUtils.isEmpty(receptionList) || !receptionList.contains(inquiryPref)) {
            return respVO;
        }
        // 因为是leftPush，所以需要反转,尾部是先进去的，需要排在前面
        List<String> list = new ArrayList<>(receptionList);
        Collections.reverse(list);
        return InquiryQueueingRespVO.builder().index(list.indexOf(inquiryPref) + 1).waitNum(list.indexOf(inquiryPref)).waitTime(list.indexOf(inquiryPref) < 3 ? 3 : 5).doctorConnectStatus(StringUtils.isBlank(doctorIm) ? 0 : 1)
            .inquiryStatus(inquiryDO.getInquiryStatus()).doctorImAccount(doctorIm).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getInquiryImRecord(String pref) {

        InquiryRecordDO inquiryRecordDO = getInquiryByPref(pref);

        if (StringUtils.isBlank(inquiryRecordDO.getImPdf())) {
            throw exception0(-1, "问诊记录生成中,请稍后重试");
        }

        Integer dateType = tenantParamConfigApi.getTenantPresDateType(inquiryRecordDO.getTenantId());

        if (Objects.equals(dateType, PrescriptionDateTypeEnum.YYYY_MM_DD_HH_MM_SS.getCode())) {
            return inquiryRecordDO.getImPdf();
        }

        InquiryRecordDetailDO recordDetailDO = getInquiryRecordDetailByPref(pref);
        // 生成年月日的im聊天记录 并存储
        if (StringUtils.isBlank(recordDetailDO.extGet().getImPdf())) {
            String imInquiryPdf = inquiryImMessageApi.getImInquiryPdf(pref);
            if (StringUtils.isBlank(imInquiryPdf)) {
                throw exception0(-1, "问诊记录生成中,请稍后重试");
            }
            inquiryRecordDetailMapper.updateById(InquiryRecordDetailDO.builder().id(recordDetailDO.getId()).ext(recordDetailDO.extGet().setImPdf(imInquiryPdf)).build());
        }

        return recordDetailDO.getExt().getImPdf();
    }


    /**
     * 根据不同的查询来源，组装不同的查询参数
     *
     * @param pageReqVO
     * @param pageReqVO 查询参数
     */
    private void handlerParam(InquiryRecordPageReqVO pageReqVO) {
        // 查询当前用户
        AdminUserRespDTO user = userApi.getUser();
        // 管理后台查询问诊记录无需处理
        if (QuerySourceEnum.WEB.equals(pageReqVO.getQuerySource()) && TenantConstant.isSystemTenant()) {
            checkWebQueryData(pageReqVO, user);
            return;
        }
        // app端查询问诊记录，查询多少天的范围走配置
        if ((QuerySourceEnum.APP.equals(pageReqVO.getQuerySource()) || QuerySourceEnum.ZHL.equals(pageReqVO.getQuerySource())) && ObjectUtil.isEmpty(pageReqVO.getCreateTime())) {
            pageReqVO.setCreateTime(BusinessUtil.getBeforeDays(MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(InquiryConstant.TENANT_QUERY_INQUIRY_RANGE_DAY), 180)));
        }
        // 小程序用户查询
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Boolean isMember = (loginUser != null && ObjectUtil.equals(loginUser.getUserType(), UserTypeEnum.MEMBER.getValue()));
        if (!QuerySourceEnum.ZHL.equals(pageReqVO.getQuerySource()) && isMember) {
            pageReqVO.setCreator(String.valueOf(loginUser.getId()));
        }
        // 门店查询 兼容总部查门店
        if (!TenantConstant.isSystemTenant() && (QuerySourceEnum.ZHL.equals(pageReqVO.getQuerySource()) || !isMember)) {
            // 需要设置门店id
            if (pageReqVO.getTenantId() != null) {
                pageReqVO.setTenantIds(Collections.singletonList(pageReqVO.getTenantId()));
            } else {
                List<Long> tenantIds = tenantApi.getTenantIdsByHeadId();
                pageReqVO.setTenantIds(CollUtil.isEmpty(tenantIds) ? Collections.singletonList(TenantContextHolder.getRequiredTenantId()) : tenantIds);
            }
            pageReqVO.setTenantId(null);
            pageReqVO.setEnable(Optional.ofNullable(pageReqVO.getEnable()).orElse(CommonStatusEnum.ENABLE.getStatus())); // 为空查可用的

            // 处理时间展示
            pageReqVO.setDateType(tenantParamConfigApi.getTenantPresDateType(TenantContextHolder.getRequiredTenantId()));
        }
        // 医生查询
        if (RoleCodeEnum.doctor(user.getRoleCodes())) {
            // 根据id查询医生id
            Optional.ofNullable(inquiryDoctorApi.getInquiryDoctorByUserId(user.getId())).ifPresent(doctorRespDto -> fillDoctorParam(pageReqVO, doctorRespDto));
        }
    }

    /**
     * PC 端查询问诊单，控制查询权限
     *
     * @param pageReqVO
     * @param user
     */
    private void checkWebQueryData(InquiryRecordPageReqVO pageReqVO, AdminUserRespDTO user) {
        // 非医院员工，则不需要处理
        if (!RoleCodeEnum.isHospitalEmployee(user.getRoleCodes())) {
            return;
        }
        // 查询当前用户绑定的医院
        List<String> hospitalList = inquiryHospitalEmployeeApi.getHospitalByUserId(user.getId());
        pageReqVO.setHospitalEmployee(Boolean.TRUE);
        if (CollUtil.isEmpty(hospitalList)) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "暂无绑定的医院");
        }
        // 构建医院查询条件
        InquiryHosDataConfigQueryParamDto queryParamDto = inquiryHospitalDataConfigApi.queryHosDataConfigParam(StringUtils.isNotBlank(pageReqVO.getHospitalPref()) ? pageReqVO.getHospitalPref() : hospitalList.getFirst());
        pageReqVO.setHosQueryParamDto(queryParamDto);
    }

    /**
     * 医生查看问诊单时，需要根据医生信息来填充查询参数
     *
     * @param pageReqVO
     * @param doctorRespDto
     */
    private void fillDoctorParam(InquiryRecordPageReqVO pageReqVO, InquiryDoctorDto doctorRespDto) {
        pageReqVO.setDoctorPref(doctorRespDto.getPref());
        // app 医生app端只查真人问诊的问诊记录
        pageReqVO.setAutoInquiry(AutoInquiryEnum.NO.getCode());
    }

}
